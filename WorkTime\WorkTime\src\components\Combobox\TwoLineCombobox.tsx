import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import Image from "../Image";
import Arrow from "../../assets/images/arrows/down-filled-triangular-arrow.png";
import Translator from "../../services/language/Translator";
import Container from "../Container";

export interface TwoLineOption {
  id: string;
  title: string;
  subtitle?: string;
}

interface TwoLineComboboxProps {
  options: TwoLineOption[];
  onChange: (option: TwoLineOption) => void;
  initialSelectedId?: string | null;
  placeholder?: string;
  placeholderWhenOpen?: string;
  isHidden?: boolean;
  disabled?: boolean;
}

const OutsideContainer = styled.div<{ $isHidden: boolean; $isOpen: boolean }>`
  position: relative;
  min-width: 15rem;
  opacity: ${({ $isHidden }) => ($isHidden ? "0" : "1")};
  transition: 0.3s ease-in-out opacity;
  z-index: ${({ $isOpen }) => ($isOpen ? 3000 : 1)};
`;

const Rows = styled(Container)<{ $isOpen: boolean; $disabled?: boolean }>`
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 3.8rem;
  margin-top: 0.08rem;
  background-color: ${({ $disabled }) =>
    $disabled
      ? "var(--input-field-background-color-disabled, #f5f5f5)"
      : "var(--combobox-background-color)"};
  border-radius: 2rem;
  transition: border-radius 0.5s;
  z-index: ${({ $isOpen }) => ($isOpen ? 2000 : 1000)};
  opacity: ${({ $disabled }) => ($disabled ? 0.6 : 1)};
  overflow: visible;
`;

const ArrowImage = styled(Image)<{ $isOpen: boolean }>`
  position: absolute;
  top: 40%;
  right: 5%;
  z-index: 2;
  width: 0.8rem;
  height: 0.8rem;
  transform: ${({ $isOpen }) => ($isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  transition: transform 0.3s ease;
  pointer-events: none;
`;

const Rectangle = styled.div<{
  $isOpen: boolean;
  $index: number;
  $isLast: boolean;
  $disabled?: boolean;
  $chosen?: boolean;
}>`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: absolute;
  width: 100%;
  background-color: ${({ $disabled }) =>
    $disabled
      ? "var(--input-field-background-color-disabled, #f5f5f5)"
      : "var(--combobox-background-color)"};
  z-index: ${({ $chosen }) => ($chosen ? 2 : 1)};
  top: ${({ $isOpen, $chosen, $index }) =>
    $isOpen && !$chosen ? `calc( 4.6rem * ${$index})` : "0"};
  border-radius: ${({ $isOpen, $chosen, $isLast }) =>
    $isOpen && !$chosen ? ($isLast ? "0rem 0rem 2rem 2rem" : "0rem") : "2rem"};
  transition: top 0.5s, border-radius 0.4s;
  justify-content: center;
  gap: 0.1rem;
  padding: 0.5rem 1rem;
  align-items: flex-start;
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
  min-height: ${({ $isOpen, $chosen }) =>
    $isOpen && !$chosen ? "4.6rem" : "3.8rem"};
`;

const Title = styled.span<{ $disabled?: boolean }>`
  font-size: 0.9rem;
  font-weight: 500;
  color: ${({ $disabled }) =>
    $disabled
      ? "var(--textbox-label-disabled-color, #999)"
      : "var(--combobox-text-color)"};
`;

const Subtitle = styled.span<{ $disabled?: boolean }>`
  font-size: 0.85rem;
  font-style: italic;
  color: ${({ $disabled }) =>
    $disabled
      ? "var(--textbox-label-disabled-color, #999)"
      : "var(--textbox-label-blur-color)"};
  width: 100%;
  text-align: left;
  align-self: center;
  white-space: normal;
  overflow-wrap: anywhere;
  word-break: break-word;
`;

const DropdownPanel = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--combobox-background-color);
  border-radius: 0rem 0rem 2rem 2rem;
  z-index: 1000;
  max-height: 18rem;
  overflow-y: auto;
`;

const OptionRow = styled.div<{ $disabled?: boolean }>`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 0.1rem 1rem;
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
`;

const TwoLineCombobox: React.FC<TwoLineComboboxProps> = ({
  options,
  onChange,
  initialSelectedId,
  placeholder,
  placeholderWhenOpen,
  isHidden,
  disabled,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState<TwoLineOption | null>(null);
  const comboboxRef = useRef<HTMLDivElement | null>(null);

  const getInitialSelected = (): TwoLineOption | null => {
    if (initialSelectedId) {
      return options.find((o) => o.id === initialSelectedId) ?? null;
    }
    return options[0] ?? null;
  };

  useEffect(() => {
    setSelected(getInitialSelected());
  }, [initialSelectedId, options]);

  const handleRectClick = (option: TwoLineOption) => {
    if (disabled) return;
    if (selected?.id === option.id) {
      setIsOpen(!isOpen);
    } else {
      setSelected(option);
      setIsOpen(false);
      onChange(option);
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      comboboxRef.current &&
      !comboboxRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    const onDown = (e: MouseEvent) => handleClickOutside(e);
    document.addEventListener("mousedown", onDown);
    return () => {
      document.removeEventListener("mousedown", onDown);
    };
  }, [comboboxRef, isOpen]);

  // currentList unused after switching to floating dropdown

  return (
    <OutsideContainer
      $isHidden={isHidden ?? false}
      $isOpen={isOpen}
      ref={comboboxRef}
    >
      <Rows
        $isOpen={isOpen}
        $disabled={disabled}
        onClick={() => {
          if (!disabled && !isOpen) setIsOpen(true);
        }}
      >
        {isOpen && placeholderWhenOpen !== undefined && (
          <Rectangle
            $index={0}
            key="combobox-header"
            $isOpen={isOpen}
            $isLast={false}
            style={{ cursor: "default", borderRadius: "2rem 2rem 0rem 0rem" }}
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(false);
            }}
          >
            <Title $disabled={disabled}>{String(placeholderWhenOpen)}</Title>
          </Rectangle>
        )}
        {isOpen ? (
          <DropdownPanel
            data-testid="two-line-dropdown"
            onMouseDown={(e) => e.stopPropagation()}
          >
            {options.map((option, index) => (
              <OptionRow
                data-testid={`two-line-combobox-option-${index}`}
                key={option.id}
                $disabled={disabled}
                onClick={() => handleRectClick(option)}
              >
                <Title $disabled={disabled}>
                  <Translator getString={option.title} />
                </Title>
                {option.subtitle && (
                  <Subtitle $disabled={disabled}>
                    <Translator getString={option.subtitle} />
                  </Subtitle>
                )}
              </OptionRow>
            ))}
          </DropdownPanel>
        ) : (
          <Rectangle
            $index={1}
            key={selected?.id ?? "__selected__"}
            $isOpen={false}
            $isLast={true}
            $disabled={disabled}
            $chosen={true}
            onMouseDown={(e) => {
              if (disabled) return;
              e.stopPropagation();
              setIsOpen(true);
            }}
          >
            <Title $disabled={disabled}>
              <Translator
                getString={selected?.title ?? String(placeholder ?? "")}
              />
            </Title>
            {selected?.subtitle && (
              <Subtitle $disabled={disabled}>
                <Translator getString={selected.subtitle} />
              </Subtitle>
            )}
          </Rectangle>
        )}
        <ArrowImage src={Arrow} alt="Arrow" $isOpen={isOpen} />
      </Rows>
    </OutsideContainer>
  );
};

export default TwoLineCombobox;
