import React, { useMemo } from "react";
import styled from "styled-components";
import { ArrowButton } from "./ArrowButton";
import Label from "../Inputs/Label";
import Container from "../Container";
import { useUserEmployee } from "../../features/UserEmployeeContext";
import Combobox from "../Combobox/Combobox";
import { DefaultPermissions } from "../../constants/permissions";
import { translate } from "../../services/language/Translator";
import { getMonthWorkingDays } from "../../services/calendar/calendarService";
import { Employee } from "../../features/attendance/useFilteredEmployees";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";
import { AbsenceRows } from "./AbsenceRows";
import NonWorkingDaysInfo from "./NonWorkingDaysInfo";

interface CalendarHeaderProps {
  selectedEmployee: Employee | undefined;
  selectedMonth: number;
  selectedYear: number;
  showMyAbsences: boolean;
  usedLeavesThisMonth: number;
  usedHospitalLeavesThisMonth: number;
  officialHolidaysNumber: number;
  nonWorkingOfficialHolidays: number;
  selectedPayroll?: LightPayrollDTO;
  setSelectedPayroll: (payroll: LightPayrollDTO | undefined) => void;
  goToPrevMonth: () => void;
  goToNextMonth: () => void;
}

const HeaderContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  font-size: 1.3em;
  width: 100%;
  height: clamp(1em, 3vw, 2em);
  padding: 1em 0.3em 1em 0.6em;

  border-radius: 1.8em 1.8em 0 0;
  background-color: var(--datepicker-header-background-color);
  cursor: pointer;
  user-select: none;
`;

const LabelCentered = styled(Label)<{ fontSize: number }>`
  text-align: center;
  width: 100%;
  padding-bottom: 0.1em;
  padding-top: 0.5em;
  font: Segoe UI;
  font-size: ${({ fontSize }) => fontSize}rem;
  color: var(--datepicker-header-font-color);
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;
  margin-bottom: 0.2em;
  height: 2.5em;
  position: relative;
`;

const ComboboxContainer = styled.div`
  height: 2.5em;
  display: flex;
  align-items: center;
`;

const WorkingDaysContainer = styled.div<{ isAdditionalInfoVisible: boolean }>`
  display: flex;
  position: absolute;
  flex-direction: column;
  left: 2.5em;
  top: ${({ isAdditionalInfoVisible }) =>
    isAdditionalInfoVisible ? "1.5rem" : "0.5rem"};
  justify-content: ${({ isAdditionalInfoVisible }) =>
    isAdditionalInfoVisible ? "flex-start" : "center"};
  height: 100%;
`;

const AbsenceRow = styled.div`
  display: flex;
  align-items: left;
  gap: 0.5em;
  max-height: 1em;
  padding: 0 1em;
  margin-bottom: 0.5em;
`;

const AbsenceText = styled(Label)`
  font-size: 0.7rem;
  color: #a8ccf2;
`;

const NumberContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5em;
`;

const NumberText = styled(Label)`
  font-size: 0.7rem;
  color: #ffffff;
  min-width: 1.5em;
  text-align: right;
`;

const LabelText = styled(Label)`
  font-size: 0.7rem;
  color: #a8ccf2;
`;

export const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  selectedMonth,
  selectedEmployee,
  showMyAbsences,
  usedLeavesThisMonth,
  usedHospitalLeavesThisMonth,
  selectedPayroll,
  setSelectedPayroll,
  selectedYear,
  officialHolidaysNumber,
  nonWorkingOfficialHolidays,
  goToPrevMonth,
  goToNextMonth,
}) => {
  const { userEmployee } = useUserEmployee();

  const isAdmin = userEmployee.permissions.includes(
    DefaultPermissions.Attendances.Write
  );

  const payrollOptions = useMemo(
    () =>
      (isAdmin && !showMyAbsences && selectedEmployee
        ? selectedEmployee?.payrolls
        : userEmployee.payrolls
      ).map(
        (payroll) => `${payroll.contractNumber} - ${payroll.position.name}`
      ),
    [selectedEmployee, userEmployee.payrolls, showMyAbsences]
  );

  const initialSelectedItem = useMemo(() => {
    if (!selectedPayroll) return null;
    return `${selectedPayroll.contractNumber} - ${selectedPayroll.position.name}`;
  }, [selectedPayroll]);

  const isAdditionalInfoVisible = useMemo(() => {
    return (
      showMyAbsences || !isAdmin || (isAdmin && selectedEmployee != undefined)
    );
  }, [selectedEmployee, showMyAbsences, userEmployee.permissions]);

  const handlePayrollChange = (chosenOption: string) => {
    const payrolls =
      isAdmin && !showMyAbsences
        ? selectedEmployee?.payrolls
        : userEmployee.payrolls;

    if (payrolls === undefined) {
      setSelectedPayroll(undefined);
      return;
    }

    const selected = payrolls.find(
      (payroll) =>
        `${payroll.contractNumber} - ${payroll.position.name}` === chosenOption
    );
    setSelectedPayroll(selected as LightPayrollDTO);
  };

  const monthWorkingDays = getMonthWorkingDays(
    selectedMonth,
    selectedYear,
    nonWorkingOfficialHolidays
  );

  /*TO DO - като се измисли как ще се смятат */
  const daysWorked = 0;

  const name =
    isAdmin && !showMyAbsences && selectedEmployee
      ? selectedEmployee?.name
      : userEmployee.name;

  const payrolls =
    isAdmin && !showMyAbsences && selectedEmployee
      ? selectedEmployee?.payrolls
      : userEmployee.payrolls;

  return (
    <HeaderContainer data-testid="calendar-header">
      <WorkingDaysContainer isAdditionalInfoVisible={isAdditionalInfoVisible}>
        <AbsenceRow>
          <NumberContainer>
            <NumberText>{monthWorkingDays.toString()}</NumberText>
            <LabelText>{translate("Working days")}</LabelText>
          </NumberContainer>
        </AbsenceRow>
        {isAdditionalInfoVisible && (
          <AbsenceRow>
            <NumberContainer>
              <NumberText>{daysWorked.toString()}</NumberText>
              <LabelText>{translate("Days worked")}</LabelText>
            </NumberContainer>
          </AbsenceRow>
        )}
      </WorkingDaysContainer>
      <ArrowButton direction="left" onClick={goToPrevMonth} />
      <LabelCentered fontSize={1.4} data-testid="month-label">
        {`${String(selectedMonth + 1).padStart(2, "0")}.${selectedYear}`}
      </LabelCentered>
      {payrolls && isAdditionalInfoVisible && (
        <HeaderContent>
          <LabelCentered fontSize={1} data-testid="employee-name">
            {name}
          </LabelCentered>
          {payrolls.length > 1 && (
            <ComboboxContainer>
              <Combobox
                height={2.5}
                placeholderWhenOpen={translate("Payroll for")}
                options={payrollOptions}
                onChange={handlePayrollChange}
                initialSelectedItem={initialSelectedItem}
              />
            </ComboboxContainer>
          )}
        </HeaderContent>
      )}
      {isAdditionalInfoVisible ? (
        <AbsenceRows
          usedLeavesThisMonth={usedLeavesThisMonth}
          usedHospitalLeavesThisMonth={usedHospitalLeavesThisMonth}
        />
      ) : (
        <NonWorkingDaysInfo
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
          officialHolidaysNumber={officialHolidaysNumber}
          nonWorkingOfficialHolidays={nonWorkingOfficialHolidays}
        />
      )}
      <ArrowButton direction="right" onClick={goToNextMonth} />
    </HeaderContainer>
  );
};
