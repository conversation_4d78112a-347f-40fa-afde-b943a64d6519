import { ApproveEmployeeEditDTO } from "../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeeEditDTO } from "../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import { DeleteEmployeeEditsDTO } from "../../models/DTOs/editEmployee/DeleteEmployeeEditsDTO";
import { EditEmployeeDTO } from "../../models/DTOs/editEmployee/EditEmployeeDTO";
import { NewEmployeeDTO } from "../../models/DTOs/newEmployee/NewEmployeeDTO";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { authenticatedPost } from "../connectionService";
import {
  authenticatedDelete as authenticatedDeleteWorktime,
  authenticatedGet,
  authenticatedPost as authenticatedPostWorktime,
  authenticatedPut as authenticatedPutWorktime,
} from "../worktimeConnectionService";

export const addNewEmployeePayroll = async (newEmployeeDTO: NewEmployeeDTO) => {
  return await authenticatedPost<EmployeePayrollDTO>(
    `employee-payroll`,
    newEmployeeDTO
  );
};

export const editEmployee = async (editEmployeeDTO: EditEmployeeDTO) => {
  return await authenticatedPutWorktime<EditEmployeeDTO>(
    `employee`,
    editEmployeeDTO
  );
};

export const approveEmployeeEdit = async (
  approveEmployeeEditDTO: ApproveEmployeeEditDTO
) => {
  return await authenticatedPostWorktime(
    `employees/approve-edit`,
    approveEmployeeEditDTO
  );
};

export const declineEmployeeEdit = async (
  declineEmployeeEditDTO: DeclineEmployeeEditDTO
) => {
  return await authenticatedPostWorktime(
    `employees/decline-edit`,
    declineEmployeeEditDTO
  );
};

export const deleteEmployeeEdits = async (
  deleteEmployeeEditDTO: DeleteEmployeeEditsDTO
) => {
  return await authenticatedDeleteWorktime(
    `employees/edits/`,
    deleteEmployeeEditDTO.employeeId
  );
};

export const doesEmployeeExistInCompany = async (
  email: string,
  egn: string,
  companyId: string
) => {
  return await authenticatedGet(
    `employee-exists?email=${email}&egn=${egn}&companyId=${companyId}`
  );
};

export function isEGNValid(egn: string): boolean {
  if (!egn || egn.length !== 10 || !/^[0-9]+$/.test(egn)) return false;
  return true;
}

export function getBirthDateFromEGN(egn: string): Date | null {
  if (!isEGNValid(egn)) return null;
  const year = parseInt(egn.substring(0, 2), 10);
  let month = parseInt(egn.substring(2, 4), 10);
  const day = parseInt(egn.substring(4, 6), 10);
  let fullYear = 1900 + year;
  let jsMonth = month;
  if (month > 40) {
    jsMonth = month - 40;
    fullYear = 2000 + year;
  } else if (month > 20) {
    jsMonth = month - 20;
    fullYear = 1800 + year;
  } else {
    jsMonth = month;
    fullYear = 1900 + year;
  }

  const parsedDate = new Date(fullYear, jsMonth - 1, day, 0, 0, 0, 0);
  if (!isNaN(parsedDate.getTime())) {
    return parsedDate;
  }
  return null;
}
