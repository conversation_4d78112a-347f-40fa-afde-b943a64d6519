import { styled } from "styled-components";
import Container from "../../../components/Container";
import { translate } from "../../../services/language/Translator";
import {
  EventType,
  EventTypeDescriptions,
} from "../../../models/DTOs/absence/EventType";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import Avatar, {
  getAbsenceAvatarColor,
} from "../../../components/CalendarComponent/Avatar";
import plane from "../../../assets/images/attendancies/airplane.svg";
import heart from "../../../assets/images/attendancies/heart.svg";
import { useEmployeesWithLeaves } from "../../attendance/useFilteredEmployees";
import { AlignmentPosition } from "../../../components/CalendarComponent/types/AlignmentPosition.ts";
import { AbsenceInfo } from "../../../components/CalendarComponent/types/AbsenceInfo";
import { selectPayrolls } from "../../payroll/payrollsActions.ts";
import { useAppSelector } from "../../../app/hooks.ts";
import { useAuth } from "../../authentication/AuthContext.tsx";
import { AbsenceHospitalDTO } from "../../../models/DTOs/absence/AbsenceHospitalDTO.ts";

const AbsenceListContainer = styled(Container)`
  margin-top: 1rem;
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const AbsenceItem = styled(Container)<{ isSelected: boolean }>`
  display: flex;
  align-items: center;
  padding: 0.4rem;
  border-radius: 1.875rem;
  background-color: ${(props) =>
    props.isSelected ? "var(--absence-side-menu-color-selected)" : "none"};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isSelected
        ? "var(--absence-side-menu-color-selected)"
        : "var(--absence-side-menu-color-hovered)"};
  }
`;

const AbsenceInfoContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex: 1;
  font-size: 0.875rem;
`;

const AbsenceDate = styled.span`
  color: var(--absence-button-color);
  margin-right: 0.5rem;
`;

const AbsenceType = styled.span`
  max-width: 10rem;
  font-size: 0.875rem;
`;

interface AbsenceListProps {
  selectedAbsence: AbsenceInfo | null;
  selectedYear: number;
  selectedMonth: number;
  onAbsenceSelect: (absence: AbsenceInfo | null) => void;
  isAdmin: boolean;
}

const formatDate = (date: Date) => {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
};

const AbsenceList = ({
  selectedAbsence,
  selectedYear,
  selectedMonth,
  onAbsenceSelect,
  isAdmin,
}: AbsenceListProps) => {
  const payrollsState = useAppSelector(selectPayrolls);
  const { user } = useAuth();

  const isSelectedAbsencePending =
    selectedAbsence &&
    (selectedAbsence.status === AbsenceStatus.Pending ||
      selectedAbsence.status == AbsenceStatus.EditedByEmployee ||
      selectedAbsence.status === AbsenceStatus.DeletedByUserAfterApproval);

  const employeesWithLeaves = useEmployeesWithLeaves(
    payrollsState.payrolls
  ).filter((e) =>
    isAdmin && isSelectedAbsencePending ? true : e.userId === user.userId
  );

  const isLeaveValid = (leave: AbsenceHospitalDTO) => {
    return isAdmin && isSelectedAbsencePending
      ? leave.status === AbsenceStatus.Pending ||
          leave.status === AbsenceStatus.EditedByEmployee ||
          leave.status === AbsenceStatus.DeletedByUserAfterApproval
      : (new Date(leave.fromDate) >= new Date(selectedYear, selectedMonth, 1) &&
          new Date(leave.fromDate) <=
            new Date(selectedYear, selectedMonth, 31)) ||
          (new Date(leave.toDate) >= new Date(selectedYear, selectedMonth, 1) &&
            new Date(leave.toDate) <=
              new Date(selectedYear, selectedMonth, 31));
  };

  return (
    <AbsenceListContainer>
      {employeesWithLeaves.map((employee) =>
        employee.payrolls
          .map((p) => p.leaves.filter((l) => isLeaveValid(l)))
          .flat()
          .map((absence) => (
            <AbsenceItem
              key={absence.id}
              isSelected={selectedAbsence?.id === absence.id}
              onClick={() => {
                if (selectedAbsence?.id === absence.id) {
                  onAbsenceSelect(null);
                } else {
                  onAbsenceSelect({
                    id: absence.id ?? "",
                    payrollId: absence.payrollId,
                    userId: employee.userId,
                    employeeName: employee.name,
                    row: 0,
                    positonRounding: AlignmentPosition.Left,
                    isHospital: absence.isHospital,
                    status: absence.status,
                    isOverlapping: absence.isOverlapping,
                    typeIdentifier: absence.typeIdentifier,
                    comment: absence.isHospital
                      ? absence.sickNote ?? ""
                      : absence.reference ?? "",
                    startDate: absence.fromDate,
                    endDate: absence.toDate,
                    exportStatus: absence.exportStatus,
                  });
                }
              }}
            >
              <Avatar
                background={getAbsenceAvatarColor(
                  absence.status,
                  absence.isHospital
                )}
                name={employee.name}
                photo={absence.isHospital ? heart : plane}
                size={1.1}
                isVisible={true}
                style={{
                  marginLeft: "0rem",
                  padding: "0.2rem",
                  marginRight: "0.5rem",
                }}
                data-testid={`employee-avatar-${absence.payrollId}`}
              />
              <AbsenceInfoContainer>
                <AbsenceType>
                  {translate(
                    EventTypeDescriptions[absence.typeIdentifier as EventType]
                  )}
                  {(absence.status === AbsenceStatus.Pending ||
                    absence.status === AbsenceStatus.EditedByEmployee ||
                    absence.status ===
                      AbsenceStatus.DeletedByUserAfterApproval) &&
                    " " + translate("for approval")}
                </AbsenceType>
                <AbsenceDate>
                  {formatDate(new Date(absence.fromDate))} -{" "}
                  {formatDate(new Date(absence.toDate))}
                </AbsenceDate>
              </AbsenceInfoContainer>
            </AbsenceItem>
          ))
      )}
    </AbsenceListContainer>
  );
};

export default AbsenceList;
