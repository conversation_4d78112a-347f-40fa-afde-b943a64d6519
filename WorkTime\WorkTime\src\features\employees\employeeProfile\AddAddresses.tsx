import { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import { translate } from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { addNewAddress } from "../../../services/addresses/addressesService";
import NewAddress from "../newEmployee/NewAddress";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
  margin-bottom: 7rem;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ isSelected: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem 0.8rem 1.8rem;
  text-align: center;
  background-color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.isSelected ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-top: 1rem;
`;

export interface EditEmployeeFormData {
  personalData: FormValidationState;
  identityCardData: FormValidationState;
}

interface FormValidationState {
  isValid: boolean;
  data: any;
}

const AddAddresses = () => {
  const { viewData, triggerEvent, closeMenu } = useMenu();
  const [activeStep, setActiveStep] = useState(viewData?.step ?? 1);
  const [isAddressDataChanged, setIsAddressDataChanged] = useState(false);
  const [addressesList] = useState(viewData?.addressesList);

  const getAllAddressPurposes = () => {
    return [
      {
        identifier: AddressPurpose.ForRemoteWork,
        label: translate("For remote work"),
      },
      { identifier: AddressPurpose.Abroad, label: translate("Abroad") },
    ];
  };

  interface Step {
    id: number;
    label: string;
    purpose: AddressPurpose | null;
    isExisting: boolean;
  }

  const createStepsFromMissingPurposes = (): Step[] => {
    if (!addressesList) return [];

    const allPurposes = getAllAddressPurposes();
    const existingPurposes = addressesList.map(
      (address: AddressDTO) => address.purpose.identifier
    );
    const missingPurposes = allPurposes.filter(
      (purpose) => !existingPurposes.includes(purpose.identifier)
    );

    const steps: Step[] = missingPurposes.map((purpose, index) => ({
      id: index + 1,
      label: purpose.label,
      purpose: purpose.identifier,
      isExisting: false,
    }));

    steps.push({
      id: steps.length + 2,
      label: translate("Other address"),
      purpose: AddressPurpose.Custom,
      isExisting: false,
    });

    return steps;
  };

  const [formDataMap, setFormDataMap] = useState<Map<number, NewAddressDTO>>(
    new Map()
  );

  const handleAddressDataChange = useCallback(() => {
    setIsAddressDataChanged(true);
  }, []);

  const handleAddressDataValidation = useCallback(
    (isValid: boolean, data: NewAddressDTO) => {
      setFormDataMap((prev) => {
        const newMap = new Map(prev);
        newMap.set(activeStep, data);
        return newMap;
      });
    },
    [activeStep]
  );

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId);
  };

  const handleSubmit = useCallback(async () => {
    try {
      const currentFormData = formDataMap.get(activeStep);
      if (currentFormData) {
        const newAddress = await addNewAddress(currentFormData);

        triggerEvent("addressAdded", newAddress);

        closeMenu();
      }
    } catch (error) {
      console.error("Error adding new address:", error);
    }
  }, [activeStep, formDataMap, triggerEvent, closeMenu]);

  const [steps, setSteps] = useState<Step[]>([]);

  useEffect(() => {
    if (addressesList) {
      const newSteps = createStepsFromMissingPurposes();
      setSteps(newSteps);

      if (newSteps.length > 0) {
        setActiveStep(newSteps[0].id);
      }

      const newFormDataMap = new Map<number, NewAddressDTO>();
      newSteps.forEach((step) => {
        newFormDataMap.set(step.id, {
          id: "",
          employeeId: viewData?.employeeId || "",
          city: null,
          country: undefined,
          district: null,
          municipality: null,
          description: "",
          block: "",
          street: "",
          apartment: "",
          postalCode: "",
          neighborhood: "",
          phone: "",
          workPhone: "",
          email: "",
          workEmail: "",
          purpose: step.purpose || AddressPurpose.IdentityCard,
          cityName: "",
          districtName: "",
          municipalityName: "",
        });
      });
      setFormDataMap(newFormDataMap);
    }
  }, [addressesList, viewData?.employeeId]);

  const renderStepContent = () => {
    const currentStep = steps.find((step) => step.id === activeStep);
    const currentFormData = formDataMap.get(activeStep);

    if (!currentStep || !currentFormData) {
      return null;
    }
    return (
      <NewAddress
        onValidation={handleAddressDataValidation}
        onChange={handleAddressDataChange}
        onSave={handleSubmit}
        data={currentFormData}
      />
    );
  };

  return (
    <MainContainer data-testid="edit-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            isSelected={activeStep === step.id}
            disabled={false}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        {renderStepContent()}
      </ContentContainer>
    </MainContainer>
  );
};

export default AddAddresses;
