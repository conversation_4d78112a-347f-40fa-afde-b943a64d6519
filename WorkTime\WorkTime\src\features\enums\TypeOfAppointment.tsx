export enum TypeOfAppointment {
  EmploymentContracts = 0,
  ManagementContracts = 1,
  SelfEmployedOwner = 2,
  CivilContractStandart = 3,
  CivilContractLandlords = 4,
  CivilContractDividends = 5,
  CivilContractOther = 6,
  EmploymentRelationship = 7,
  Candidates = 8,
  ApprovedForInterview = 9,
  PassedInterviewMeeting = 10,
  InvitedToWork = 11,
  ToConcludeContract = 12,
  RepresentativeOfAnExternalCompany = 13,
}

export const DoesTypeOfAppointmentNeededConfirmation = (
  typeOfAppointment: TypeOfAppointment
) => {
  return (
    typeOfAppointment === TypeOfAppointment.EmploymentContracts ||
    typeOfAppointment === TypeOfAppointment.EmploymentRelationship
  );
};

export const DoesTypeOfAppointmentHaveLeaves = (
  typeOfAppointment: TypeOfAppointment
) => {
  return (
    typeOfAppointment === TypeOfAppointment.EmploymentContracts ||
    typeOfAppointment === TypeOfAppointment.ManagementContracts ||
    typeOfAppointment === TypeOfAppointment.SelfEmployedOwner ||
    typeOfAppointment === TypeOfAppointment.CivilContractStandart ||
    typeOfAppointment === TypeOfAppointment.CivilContractOther ||
    typeOfAppointment === TypeOfAppointment.EmploymentRelationship ||
    typeOfAppointment === TypeOfAppointment.RepresentativeOfAnExternalCompany
  );
};
