import { useState, useEffect } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../../app/hooks";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { LOCAL_STORAGE_COMPANY_ID } from "../../../constants/local-storage-constants";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import { editEmployee } from "../../../services/employees/employeesService";
import { translate } from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";
import { EditEmployeeDTO } from "../../../models/DTOs/editEmployee/EditEmployeeDTO";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { onEmployeeEdited } from "../employeesActions";
import NewAddresses from "../newEmployee/NewAddress";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
  margin-bottom: 7rem;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ isSelected: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem 0.8rem 1.8rem;
  text-align: left;
  font-size: 0.9rem;
  background-color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.isSelected ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-top: 1rem;
`;

const NavigationContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
  justify-content: center;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 5;
`;

const NextButton = styled(Button)`
  width: 90%;
`;
export interface EditEmployeeFormData {
  personalData: FormValidationState | null;
  identityCardData: FormValidationState | null;
  addressCorrespondence: FormValidationState | null;
  addressRemoteWork: FormValidationState | null;
  addressAbroad: FormValidationState | null;
  otherAddresses: FormValidationState[] | null;
}
interface FormValidationState {
  isValid: boolean;
  data: any;
  isChanged?: boolean;
}

const EditEmployeeAddressesSideMenu = () => {
  const dispatch = useAppDispatch();
  const { toggleMenu, changeView, viewData } = useMenu();
  const [activeStep, setActiveStep] = useState<number | string>(
    viewData?.step ?? 1
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isAddressCorrespondenceChanged, setIsAddressCorrespondenceChanged] =
    useState(false);
  const [isAddressRemoteWorkChanged, setIsAddressRemoteWorkChanged] =
    useState(false);
  const [isAddressAbroadChanged, setIsAddressAbroadChanged] = useState(false);

  const convertEmployeeAddressToAddressData = (
    addresses: AddressDTO[] | undefined,
    purpose: AddressPurpose
  ): NewAddressDTO => {
    var address = addresses?.find(
      (address) => address.purpose.identifier === purpose
    );
    if (addresses === undefined || !address) {
      return {
        id: undefined,
        city: null,
        postalCode: "",
        employeeId: "",
        municipality: null,
        district: null,
        street: "",
        block: "",
        apartment: "",
        phone: "",
        workPhone: "",
        email: "",
        workEmail: "",
        country: undefined,
        purpose: purpose,
        description: "",
        neighborhood: "",
        cityName: "",
        districtName: "",
        municipalityName: "",
      };
    }
    return {
      id: address?.id || undefined,
      postalCode: address?.postalCode || "",
      employeeId: address?.employeeId || "",
      municipality: {
        id: address?.municipality?.id || "",
        name: address?.municipality?.name || "",
        districtId: address?.district?.id || "",
      },
      district: {
        id: address?.district?.id || "",
        name: address?.district?.name || "",
      },
      city: {
        id: address?.city?.id || "",
        name: address?.city?.name || "",
        postCode: "",
        EKATTECode: undefined,
        municipalityId: address?.municipality?.id || "",
      },
      street: address?.street || "",
      block: address?.block || "",
      apartment: address?.apartment || "",
      phone: address?.phone || "",
      workPhone: address?.workPhone || "",
      email: address?.email || "",
      workEmail: address?.workEmail || "",
      country: Number(address?.country) || 0,
      purpose: purpose,
      description: address?.description || "",
      neighborhood: address?.neighborhood || "",
      cityName: address?.cityName || "",
      districtName: address?.districtName || "",
      municipalityName: address?.municipalityName || "",
    };
  };

  const addressesList = viewData?.addressesList as AddressDTO[];
  const getAddressPurposeDescription = (
    purpose: number,
    addressName: string
  ): string => {
    switch (purpose) {
      case AddressPurpose.IdentityCard:
        return "Identity card";
      case AddressPurpose.ForContact:
        return "For contact";
      case AddressPurpose.ForRemoteWork:
        return "For remote work";
      case AddressPurpose.Abroad:
        return "Abroad";
      case AddressPurpose.Custom:
        return addressName;
      default:
        return "Unknown";
    }
  };

  const [formData, setFormData] = useState<EditEmployeeFormData>({
    personalData: null,
    identityCardData: null,
    addressCorrespondence: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.ForContact
      ),
    },
    addressRemoteWork: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.ForRemoteWork
      ),
    },
    addressAbroad: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.Abroad
      ),
    },
    otherAddresses: addressesList
      ?.filter(
        (address) => address.purpose.identifier === AddressPurpose.Custom
      )
      .map((address) => ({
        isValid: false,
        data: convertEmployeeAddressToAddressData(
          addressesList,
          address.purpose.identifier
        ),
      })),
  });

  useEffect(() => {
    if (addressesList) {
      setFormData((prev) => ({
        ...prev,
        personalData: {
          isValid: false,
          data: null,
        },
        identityCardData: {
          isValid: false,
          data: null,
        },
        addressCorrespondence: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.ForContact
          ),
        },
        addressRemoteWork: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.ForRemoteWork
          ),
        },
        addressAbroad: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.Abroad
          ),
        },
        otherAddresses: addressesList
          ?.filter(
            (address) => address.purpose.identifier === AddressPurpose.Custom
          )
          .map((address) => ({
            isValid: false,
            data: convertEmployeeAddressToAddressData(
              addressesList,
              address.purpose.identifier
            ),
          })),
      }));
    }
  }, [addressesList]);

  const isCurrentStepValid = () => {
    const currentStep = steps.find((step) => step.id === activeStep);
    if (!currentStep) return false;

    if (
      activeStep === formData.addressCorrespondence?.data.id ||
      activeStep === `default_${AddressPurpose.ForContact}`
    ) {
      return formData.addressCorrespondence?.isValid ?? false;
    }
    if (activeStep === formData.addressRemoteWork?.data.id) {
      return formData.addressRemoteWork?.isValid ?? false;
    }
    if (activeStep === formData.addressAbroad?.data.id) {
      return formData.addressAbroad?.isValid ?? false;
    }

    const customAddressIndex = formData.otherAddresses?.findIndex(
      (addr) => addr.data.id === activeStep
    );
    if (customAddressIndex !== undefined && customAddressIndex >= 0) {
      return formData.otherAddresses?.[customAddressIndex]?.isValid ?? false;
    }

    return false;
  };

  const isCurrentStepChanged = () => {
    const currentStep = steps.find((step) => step.id === activeStep);
    if (!currentStep) return false;

    if (
      activeStep === formData.addressCorrespondence?.data.id ||
      activeStep === `default_${AddressPurpose.ForContact}`
    ) {
      return isAddressCorrespondenceChanged;
    }
    if (activeStep === formData.addressRemoteWork?.data.id) {
      return isAddressRemoteWorkChanged;
    }
    if (activeStep === formData.addressAbroad?.data.id) {
      return isAddressAbroadChanged;
    }

    const customAddressIndex = formData.otherAddresses?.findIndex(
      (addr) => addr.data.id === activeStep
    );
    if (customAddressIndex !== undefined && customAddressIndex >= 0) {
      return formData.otherAddresses?.[customAddressIndex]?.isChanged ?? false;
    }

    return false;
  };

  const handleAddressCorrespondenceChange = () => {
    setIsAddressCorrespondenceChanged(true);
  };
  const handleAddressRemoteWorkChange = () => {
    setIsAddressRemoteWorkChanged(true);
  };
  const handleAddressAbroadChange = () => {
    setIsAddressAbroadChanged(true);
  };

  const handleAddressValidation = (isValid: boolean, data: AddressDTO) => {
    if (
      activeStep === formData.addressCorrespondence?.data.id ||
      activeStep === `default_${AddressPurpose.ForContact}`
    ) {
      setFormData((prev) => ({
        ...prev,
        addressCorrespondence: { isValid, data },
      }));
    } else if (activeStep === formData.addressRemoteWork?.data.id) {
      setFormData((prev) => ({
        ...prev,
        addressRemoteWork: { isValid, data },
      }));
    } else if (activeStep === formData.addressAbroad?.data.id) {
      setFormData((prev) => ({
        ...prev,
        addressAbroad: { isValid, data },
      }));
    } else {
      const customAddressIndex = formData.otherAddresses?.findIndex(
        (addr) => addr.data.id === activeStep
      );
      if (customAddressIndex !== undefined && customAddressIndex >= 0) {
        setFormData((prev) => ({
          ...prev,
          otherAddresses:
            prev.otherAddresses?.map((addr, idx) =>
              idx === customAddressIndex ? { isValid, data } : addr
            ) || [],
        }));
      }
    }
  };

  const generateSteps = () => {
    const baseSteps = [
      {
        id:
          formData.addressCorrespondence?.data.id ??
          `default_${AddressPurpose.ForContact}`,
        label: translate("For contact"),
        component: (
          <NewAddresses
            onValidation={handleAddressValidation}
            onChange={handleAddressCorrespondenceChange}
            data={formData.addressCorrespondence?.data as NewAddressDTO}
            isNewEmployeeFlow={true}
          />
        ),
      },
      {
        id:
          formData.addressRemoteWork?.data.id ??
          `default_${AddressPurpose.ForRemoteWork}`,
        label: translate("For remote work"),
        component: (
          <NewAddresses
            onValidation={handleAddressValidation}
            onChange={handleAddressRemoteWorkChange}
            data={formData.addressRemoteWork?.data as NewAddressDTO}
            isNewEmployeeFlow={true}
          />
        ),
      },
      {
        id:
          formData.addressAbroad?.data.id ?? `default_${AddressPurpose.Abroad}`,
        label: translate("Abroad"),
        component: (
          <NewAddresses
            onValidation={handleAddressValidation}
            onChange={handleAddressAbroadChange}
            data={formData.addressAbroad?.data as NewAddressDTO}
            isNewEmployeeFlow={true}
          />
        ),
      },
    ];

    const customAddressSteps =
      formData.otherAddresses?.map((addressData, index) => {
        const address = addressData.data as NewAddressDTO;
        const stepId = `custom_${index}`;

        return {
          id: address.id ?? stepId,
          label:
            address.description ||
            getAddressPurposeDescription(
              address.purpose,
              address.description || "Custom"
            ),
          component: (
            <NewAddresses
              onValidation={(isValid, data) => {
                setFormData((prev) => ({
                  ...prev,
                  otherAddresses:
                    prev.otherAddresses?.map((addr) =>
                      addr.data.id === address.id ? { isValid, data } : addr
                    ) || [],
                }));
              }}
              onChange={() => {
                setFormData((prev) => ({
                  ...prev,
                  otherAddresses:
                    prev.otherAddresses?.map((addr) =>
                      addr.data.id === address.id
                        ? { ...addr, isChanged: true }
                        : addr
                    ) || [],
                }));
              }}
              data={address}
              isNewEmployeeFlow={true}
            />
          ),
        };
      }) || [];

    return [...baseSteps, ...customAddressSteps];
  };

  const steps = generateSteps();

  const handleStepClick = (stepId: number | string) => {
    setActiveStep(stepId);
  };

  const handleSubmit = async () => {
    if (!isCurrentStepValid()) {
      return;
    }

    setIsLoading(true);
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    const editEmployeeData: EditEmployeeDTO = {
      employeeId: viewData?.employeeId,
      payrollId: viewData?.payrollId,
      companyId: companyId,
      personalData: null,
      identityCardData: null,
      addressForCorrespondence: isAddressCorrespondenceChanged
        ? (formData.addressCorrespondence?.data as NewAddressDTO)
        : null,
      addressForRemoteWork: isAddressRemoteWorkChanged
        ? (formData.addressRemoteWork?.data as NewAddressDTO)
        : null,
      addressForAbroad: isAddressAbroadChanged
        ? (formData.addressAbroad?.data as NewAddressDTO)
        : null,
      otherAddresses:
        formData.otherAddresses
          ?.filter((address) => address.isChanged)
          .map((address) => address.data) ?? null,
    };

    const editedEmployee = await editEmployee(editEmployeeData);
    dispatch(onEmployeeEdited(editedEmployee));

    setIsLoading(false);
    toggleMenu();
    changeView("employees", "other");
  };

  return (
    <MainContainer data-testid="edit-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            isSelected={activeStep === step.id}
            disabled={false}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        <div key={activeStep}>
          {steps.find((step) => step.id === activeStep)?.component}
        </div>
      </ContentContainer>

      <NavigationContainer data-testid="navigation-container">
        <NextButton
          label="Edit"
          onClick={handleSubmit}
          disabled={!isCurrentStepValid() || !isCurrentStepChanged()}
          data-testid="edit-address-button"
        />
      </NavigationContainer>
    </MainContainer>
  );
};

export default EditEmployeeAddressesSideMenu;
