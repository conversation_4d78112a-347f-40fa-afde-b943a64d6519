import { useEffect, useState } from "react";
import styled from "styled-components";
import addressDot from "../../../assets/images/dot-icons/addressDot.svg";
import mailDot from "../../../assets/images/dot-icons/mailDot.svg";
import phoneDot from "../../../assets/images/dot-icons/phoneDot.svg";
import Container from "../../../components/Container";
import Dropdown from "../../../components/Dropdown/Dropdown";
import {
  DropdownContainer,
  DropdownFormField,
  DropdownFormRow,
  HeaderImage,
  HeaderWrapper,
  DropdownBody as StyledDropdownBody,
  StyledInput,
  Wrapper,
} from "../../../components/Dropdown/DropdownStyles";
import Image from "../../../components/Image";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";
import Button from "../../../components/Inputs/Button";
import { AddressPurpose } from "../../../models/DTOs/address/AddressDTO";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import Translator, { translate } from "../../../services/language/Translator";
import { useDefaultPlaces } from "../../DefaultLocationDataContext";
import { useMenu } from "../../MenuContext";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;

  &:first-child {
    margin-top: 0;
  }
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  color: #333;
  white-space: nowrap;
  margin-left: 0.5rem;
`;

const SectionSeparator = styled.div`
  height: 1px;
  display: flex;
  background-color: white;
  width: 100%;
  margin-left: 0.5rem;
  margin-top: 0.3rem;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 0.3rem;
  width: 100%;
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const SingleFieldRow = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
`;

const DropdownBody = styled(StyledDropdownBody).attrs({ as: Dropdown.Body })`
  background: var(--textbox-color);
`;

const SaveButtonContainer = styled(Container)`
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  border-top: 1px solid #e0e0e0;
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 5;
  width: 100%;
`;

const SaveButton = styled(Button)`
  width: 100%;
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  data?: NewAddressDTO;
  onChange?: () => void;
  onSave?: () => void;
  isNewEmployeeFlow?: boolean;
}

const NewAddress = ({
  onValidation,
  data,
  onChange,
  onSave,
  isNewEmployeeFlow,
}: Props) => {
  const { cities, districts, municipalities } = useDefaultPlaces();
  const { viewData } = useMenu();

  const [formData, setFormData] = useState<NewAddressDTO>({
    id: data?.id || undefined,
    employeeId: viewData?.employeeId || undefined,
    city: data?.city || null,
    postalCode: data?.postalCode || "",
    municipality: data?.municipality || null,
    district: data?.district || null,
    street: data?.street || "",
    block: data?.block || "",
    apartment: data?.apartment || "",
    phone: data?.phone || "",
    workPhone: data?.workPhone || "",
    email: data?.email || "",
    workEmail: data?.workEmail || "",
    country: data?.country || undefined,
    purpose: data?.purpose ?? AddressPurpose.ForContact,
    description: data?.description || "",
    neighborhood: data?.neighborhood || "",
    cityName: data?.cityName || "",
    districtName: data?.districtName || "",
    municipalityName: data?.municipalityName || "",
  });

  useEffect(() => {
    if (data) {
      setFormData((prev) => ({
        ...prev,
        id: data.id || prev.id,
        employeeId: viewData?.employeeId || prev.employeeId,
        city: data.city || null,
        postalCode: data.postalCode || prev.postalCode,
        municipality: data.municipality || null,
        district: data.district || null,
        street: data.street || prev.street,
        block: data.block || prev.block,
        apartment: data.apartment || prev.apartment,
        phone: data.phone || prev.phone,
        workPhone: data.workPhone || prev.workPhone,
        email: data.email || prev.email,
        workEmail: data.workEmail || prev.workEmail,
        country: data.country || prev.country,
        purpose: data.purpose ?? prev.purpose,
        description: data.description || prev.description,
        neighborhood: data.neighborhood || prev.neighborhood,
        cityName: data.cityName || prev.cityName,
        districtName: data.districtName || prev.districtName,
        municipalityName: data.municipalityName || prev.municipalityName,
      }));
    }
  }, [data, viewData?.employeeId]);

  const postCodes = Array.from(
    new Set(
      cities
        .map((city) => city.postCode)
        .filter((postCode) => postCode !== undefined)
    )
  );

  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [isDistrictDropdownOpen, setIsDistrictDropdownOpen] = useState(false);
  const [isMunicipalityDropdownOpen, setIsMunicipalityDropdownOpen] =
    useState(false);
  const [isPostalCodeDropdownOpen, setIsPostalCodeDropdownOpen] =
    useState(false);

  const [citySearchText, setCitySearchText] = useState("");
  const [districtSearchText, setDistrictSearchText] = useState("");
  const [municipalitySearchText, setMunicipalitySearchText] = useState("");
  const [postalCodeSearchText, setPostalCodeSearchText] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value,
    };

    setFormData(newFormData);
    onValidation?.(true, newFormData);
    if (onChange) {
      onChange();
    }
  };

  useEffect(() => {
    const isValid = true;

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, []);

  const handleCityDropdownToggle = (isOpen: boolean) => {
    setIsCityDropdownOpen(isOpen);
  };

  const handleDistrictDropdownToggle = (isOpen: boolean) => {
    setIsDistrictDropdownOpen(isOpen);
  };

  const handleMunicipalityDropdownToggle = (isOpen: boolean) => {
    setIsMunicipalityDropdownOpen(isOpen);
  };

  const handleCityTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCitySearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      city: null,
      cityName: e.target.value,
    }));
    setIsCityDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleDistrictTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDistrictSearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      district: null,
      districtName: e.target.value,
    }));
    setIsDistrictDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleMunicipalityTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setMunicipalitySearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      municipality: null,
      municipalityName: e.target.value,
    }));
    setIsMunicipalityDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handleCitySelect = (city: any) => {
    setCitySearchText(city.name);

    const matchingMunicipality = municipalities.find(
      (municipality) => municipality.id === city.municipalityId
    );

    const matchingDistrict = districts.find(
      (district) => district.id === matchingMunicipality?.districtId
    );

    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        postalCode:
          cities
            .filter((c) => c.name === city.name)
            .sort((a, b) =>
              (a.postCode || "").localeCompare(b.postCode || "")
            )[0]?.postCode || prev.postalCode,
        city: city,
        cityName: city.name,
        ...(matchingDistrict && {
          district: matchingDistrict,
          districtName: matchingDistrict.name,
        }),
        ...(matchingMunicipality && {
          municipality: matchingMunicipality,
          municipalityName: matchingMunicipality.name,
        }),
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    setIsCityDropdownOpen(false);
    if (onChange) {
      onChange();
    }
  };

  const handleDistrictSelect = (district: any) => {
    setDistrictSearchText(district.name);
    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        district: district,
        districtName: district.name,
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    if (onChange) {
      onChange();
    }

    setIsDistrictDropdownOpen(false);
  };

  const handleMunicipalitySelect = (municipality: any) => {
    setMunicipalitySearchText(municipality.name);
    setFormData((prev: NewAddressDTO) => {
      const newFormData = {
        ...prev,
        municipality: municipality,
        municipalityName: municipality.name,
      };
      if (onValidation) {
        onValidation(true, newFormData);
      }
      return newFormData;
    });

    if (onChange) {
      onChange();
    }

    setIsMunicipalityDropdownOpen(false);
  };

  const filteredCities = cities
    .filter(
      (city) =>
        city.postCode &&
        city.postCode !== "" &&
        (!formData.municipality ||
          formData.municipality?.id === city.municipalityId) &&
        city.name.toLowerCase().startsWith(citySearchText.toLowerCase())
    )
    .filter(
      (city, index, array) =>
        array.findIndex((c) => c.name === city.name) === index
    )
    .sort((a, b) => {
      const municipalityName = formData?.municipality?.name?.toLowerCase();
      const aIsMatch = a.name.toLowerCase() === municipalityName;
      const bIsMatch = b.name.toLowerCase() === municipalityName;

      return (
        (bIsMatch ? 1 : 0) - (aIsMatch ? 1 : 0) || a.name.localeCompare(b.name)
      );
    });

  const filteredDistricts = districts
    .filter((district) =>
      district.name.toLowerCase().startsWith(districtSearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const filteredMunicipalities = municipalities
    .filter(
      (municipality) =>
        (!formData.district ||
          formData.district?.id === municipality.districtId) &&
        municipality.name
          .toLowerCase()
          .startsWith(municipalitySearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const filteredPostCodes = postCodes
    .filter((postCode) => postCode?.includes(postalCodeSearchText))
    .sort((a, b) => a.localeCompare(b));

  const handlePostalCodeDropdownToggle = (isOpen: boolean) => {
    setIsPostalCodeDropdownOpen(isOpen);
  };

  const handlePostalCodeTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPostalCodeSearchText(e.target.value);
    setFormData((prev: NewAddressDTO) => ({
      ...prev,
      postalCode: e.target.value,
    }));
    setIsPostalCodeDropdownOpen(true);
    if (onChange) {
      onChange();
    }
  };

  const handlePostalCodeSelect = (postCode: string) => {
    setPostalCodeSearchText(postCode);

    let matchingCity = cities.find(
      (city) =>
        city.postCode === postCode &&
        city.name.toLowerCase() ===
          municipalities
            .find((municipality) => municipality.id === city.municipalityId)
            ?.name?.toLowerCase()
    );

    if (!matchingCity) {
      matchingCity = cities.find((city) => city.postCode === postCode);
    }

    if (matchingCity) {
      const matchingMunicipality = municipalities.find(
        (municipality) => municipality.id === matchingCity.municipalityId
      );

      const matchingDistrict = districts.find(
        (district) => district.id === matchingMunicipality?.districtId
      );

      setFormData((prev: NewAddressDTO) => {
        const newFormData = {
          ...prev,
          postalCode: postCode,
          city: matchingCity,
          cityName: matchingCity.name,
          ...(matchingDistrict && {
            district: matchingDistrict,
            districtName: matchingDistrict.name,
          }),
          ...(matchingMunicipality && {
            municipality: matchingMunicipality,
            municipalityName: matchingMunicipality.name,
          }),
        };
        if (onValidation) {
          onValidation(true, newFormData);
        }
        return newFormData;
      });
    } else {
      setFormData((prev: NewAddressDTO) => {
        const newFormData = {
          ...prev,
          postalCode: postCode,
        };
        if (onValidation) {
          onValidation(true, newFormData);
        }
        return newFormData;
      });
    }

    setIsPostalCodeDropdownOpen(false);
    if (onChange) {
      onChange();
    }
  };

  const handleSave = async () => {
    if (onSave) {
      onSave();
    }
  };

  return (
    <FormContainer>
      <SectionHeader>
        <Image src={addressDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("Address")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      {!isNewEmployeeFlow && formData.purpose === AddressPurpose.Custom && (
        <SingleFieldRow>
          <FormField>
            <Textbox
              name="description"
              label={translate("Address name")}
              value={formData.description}
              handleChange={handleChange}
            />
          </FormField>
        </SingleFieldRow>
      )}

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleCityDropdownToggle}
              isOpen={isCityDropdownOpen}
              data-testid="city-dropdown"
            >
              <Dropdown.Header data-testid="dropdown-header">
                <HeaderWrapper
                  isOpen={isCityDropdownOpen}
                  data-testid="header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={citySearchText || formData.city?.name || ""}
                    placeholder={translate("strCityVillage")}
                    onChange={handleCityTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isCityDropdownOpen}
                  data-testid="header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="dropdown-body">
                {filteredCities.map((city) => (
                  <Wrapper
                    isOpen={isCityDropdownOpen}
                    key={city.id}
                    onClick={() => handleCitySelect(city)}
                    data-testid={`city-wrapper-${city.id}`}
                  >
                    <Translator getString={city.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>

        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handlePostalCodeDropdownToggle}
              isOpen={isPostalCodeDropdownOpen}
              data-testid="postal-code-dropdown"
            >
              <Dropdown.Header data-testid="postal-code-dropdown-header">
                <HeaderWrapper
                  isOpen={isPostalCodeDropdownOpen}
                  data-testid="postal-code-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={postalCodeSearchText || formData.postalCode || ""}
                    placeholder={translate("PC")}
                    onChange={handlePostalCodeTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isPostalCodeDropdownOpen}
                  data-testid="postal-code-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="postal-code-dropdown-body">
                {filteredPostCodes.map((postCode) => (
                  <Wrapper
                    isOpen={isPostalCodeDropdownOpen}
                    key={postCode}
                    onClick={() => handlePostalCodeSelect(postCode)}
                    data-testid={`postal-code-wrapper-${postCode}`}
                  >
                    {postCode}
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
      </DropdownFormRow>

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleDistrictDropdownToggle}
              isOpen={isDistrictDropdownOpen}
              data-testid="district-dropdown"
            >
              <Dropdown.Header data-testid="district-dropdown-header">
                <HeaderWrapper
                  isOpen={isDistrictDropdownOpen}
                  data-testid="district-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={districtSearchText || formData.district?.name || ""}
                    placeholder={translate("District")}
                    onChange={handleDistrictTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isDistrictDropdownOpen}
                  data-testid="district-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="district-dropdown-body">
                {filteredDistricts.map((district) => (
                  <Wrapper
                    isOpen={isDistrictDropdownOpen}
                    key={district.name}
                    onClick={() => handleDistrictSelect(district)}
                    data-testid={`district-wrapper-${district.name}`}
                  >
                    <Translator getString={district.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleMunicipalityDropdownToggle}
              isOpen={isMunicipalityDropdownOpen}
              data-testid="municipality-dropdown"
            >
              <Dropdown.Header data-testid="municipality-dropdown-header">
                <HeaderWrapper
                  isOpen={isMunicipalityDropdownOpen}
                  data-testid="municipality-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={
                      municipalitySearchText ||
                      formData.municipality?.name ||
                      ""
                    }
                    placeholder={translate("Municipality")}
                    onChange={handleMunicipalityTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isMunicipalityDropdownOpen}
                  data-testid="municipality-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="municipality-dropdown-body">
                {filteredMunicipalities.map((municipality) => (
                  <Wrapper
                    isOpen={isMunicipalityDropdownOpen}
                    key={municipality.name}
                    onClick={() => handleMunicipalitySelect(municipality)}
                    data-testid={`municipality-wrapper-${municipality.name}`}
                  >
                    <Translator getString={municipality.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
      </DropdownFormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="neighborhood"
            label="Region"
            value={formData.neighborhood}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="street"
            label="Street"
            value={formData.street}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="block"
            label="Block"
            value={formData.block}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="apartment"
            label="Apartment"
            value={formData.apartment}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <SectionHeader>
        <Image src={phoneDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("Phone number")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="phone"
            label="Phone number"
            value={formData.phone}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="workPhone"
            label="Work phone"
            value={formData.workPhone}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <SectionHeader>
        <Image src={mailDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("E-mail")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <SingleFieldRow>
        <FormField>
          <Textbox
            name="email"
            label="Е-mail"
            value={formData.email}
            handleChange={handleChange}
          />
        </FormField>
      </SingleFieldRow>

      <SingleFieldRow>
        <FormField>
          <Textbox
            name="workEmail"
            label="Work E-mail"
            value={formData.workEmail}
            handleChange={handleChange}
          />
        </FormField>
      </SingleFieldRow>

      {!isNewEmployeeFlow && (
        <SaveButtonContainer>
          <SaveButton
            label={translate("Add")}
            onClick={handleSave}
            data-testid="save-address-button"
          />
        </SaveButtonContainer>
      )}
    </FormContainer>
  );
};

export default NewAddress;
