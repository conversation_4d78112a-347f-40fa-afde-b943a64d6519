import { IdentityCardDataDTO } from "../newEmployee/IdentityCardDataDTO";
import { NewAddressDTO } from "../newEmployee/NewAddressDTO";
import { PersonalDataDTO } from "../newEmployee/PersonalDataDTO";

export interface EditEmployeeDTO {
  employeeId: string;
  payrollId: string;
  companyId: string;
  personalData: PersonalDataDTO | null;
  identityCardData: IdentityCardDataDTO | null;
  addressForCorrespondence: NewAddressDTO | null;
  addressForRemoteWork: NewAddressDTO | null;
  addressForAbroad: NewAddressDTO | null;
  otherAddresses: NewAddressDTO[] | null;
}
