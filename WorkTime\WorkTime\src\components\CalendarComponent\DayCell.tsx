import React from "react";
import styled from "styled-components";
import { Employee } from "../../features/attendance/useFilteredEmployees";
import Container from "../Container";
import DayBox from "./DayBox";
import { AbsenceInfo } from "./types/AbsenceInfo";

interface DayCellProps {
  dayDate: Date;
  dayNumber: number;
  isCurrentMonth: boolean;
  isCurrentDay: boolean;
  isHoliday: boolean;
  missingEmployees: AbsenceInfo[];
  numberOfRows: number;
  selectedEmployee?: Employee;
  hoveredEmployee?: Employee;
  isWeekend: boolean;
  showMyAbsences: boolean;
  onSelectEmployee?: (employee: Employee | undefined) => void;
}

const ContainerDay = styled(Container)<{
  $isCurrentMonth: boolean;
  $numberOfRows: number;
  $isWeekend: boolean;
  $isHoliday: boolean;
}>`
  display: grid;
  position: relative;

  grid-template-columns: repeat(1, 1fr);
  max-width: 11rem;
  height: ${({ $numberOfRows }) =>
    $numberOfRows <= 4
      ? `calc(3.5em + ${2 * 2}em)`
      : `calc(${$numberOfRows * 1.6}em)`};

  font-size: clamp(0.75em, 2vw, 1em);
  cursor: pointer;

  background-color: ${({ $isCurrentMonth, $isWeekend, $isHoliday }) =>
    !$isCurrentMonth
      ? "var(--datepicker-view-buttons-color)"
      : $isHoliday
      ? "var(--datepicker-view-buttons-color-holiday)"
      : $isWeekend
      ? "var(--datepicker-view-buttons-color-weekend)"
      : "var(--datepicker-view-background-color)"};

  color: ${({ $isCurrentMonth }) =>
    $isCurrentMonth
      ? "var(--absence-button-color-disable)"
      : "var(--datepicker-view-buttons-font-color-not-current-month)"};

  &::after,
  &::before {
    content: "";
    position: absolute;
    background-color: ${({ $isCurrentMonth }) =>
      $isCurrentMonth
        ? "var(--datepicker-view-buttons-color)"
        : "var(--datepicker-view-background-color)"};
    z-index: 1;
  }

  &::after {
    top: -0.1em;
    left: 0;
    width: 100vw;
    height: 0.075em;
  }

  &::before {
    top: 0;
    left: -0.12em;
    width: 0.05em;
    height: 100vh;
  }

  user-select: none;
`;

const Circle = styled(Container)<{ isCurrentDay: boolean }>`
  width: 1.2em;
  height: 1.2em;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${({ isCurrentDay }) => (isCurrentDay ? "0.25em" : "0")};
  margin: 0;
  border-radius: 50%;
  border: ${({ isCurrentDay }) =>
    isCurrentDay
      ? "0.125rem solid var(--datepicker-selected-day-color)"
      : "0.125rem solid transparent"};
`;

export const DayCell: React.FC<DayCellProps> = ({
  dayDate,
  dayNumber,
  isCurrentMonth,
  isCurrentDay,
  isHoliday,
  missingEmployees,
  numberOfRows,
  selectedEmployee,
  hoveredEmployee,
  isWeekend,
  showMyAbsences,
}) => {
  return (
    <ContainerDay
      data-testid="day-cell-container"
      $isCurrentMonth={isCurrentMonth}
      $numberOfRows={numberOfRows}
      $isWeekend={isWeekend}
      $isHoliday={isHoliday}
    >
      <Circle
        data-testid="day-cell-circle"
        isCurrentDay={isCurrentDay && isCurrentMonth}
      >
        {dayNumber}
      </Circle>
      <DayBox
        dayDate={dayDate}
        data-testid="day-cell-box"
        dayData={missingEmployees}
        numberOfRows={numberOfRows}
        selectedEmployee={selectedEmployee}
        hoveredEmployee={hoveredEmployee}
        showMyAbsences={showMyAbsences}
      />
    </ContainerDay>
  );
};
