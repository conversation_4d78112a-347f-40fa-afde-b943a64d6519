import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";

export const calculateWorkingDays = (
  startDate: Date,
  endDate: Date
): number => {
  let workingDays = 0;
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      workingDays++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return workingDays;
};

export const calculateDaysInSelectedYear = (
  leave: AbsenceHospitalDTO,
  selectedYear: string
): number => {
  const selectedYearNum = parseInt(selectedYear);
  const fromDate = new Date(leave.fromDate);
  const toDate = new Date(leave.toDate);
  const yearStart = new Date(selectedYearNum, 0, 1);
  const yearEnd = new Date(selectedYearNum, 11, 31);

  if (fromDate > yearStart && toDate < yearEnd) {
    return leave.duration;
  }

  const effectiveStartDate = fromDate < yearStart ? yearStart : fromDate;
  const effectiveEndDate = toDate > yearEnd ? yearEnd : toDate;

  if (effectiveStartDate > yearEnd || effectiveEndDate < yearStart) {
    return 0;
  }

  return calculateWorkingDays(effectiveStartDate, effectiveEndDate);
};

export const calculateLeaveDaysInPeriod = (
  leave: AbsenceHospitalDTO,
  startDate: Date,
  endDate: Date
): number => {
  const fromDate = new Date(leave.fromDate);
  const toDate = new Date(leave.toDate);
  const yearStart = new Date(startDate);
  const yearEnd = new Date(endDate);

  if (fromDate > yearStart && toDate < yearEnd) {
    return leave.duration;
  }

  const effectiveStartDate = fromDate < yearStart ? yearStart : fromDate;
  const effectiveEndDate = toDate > yearEnd ? yearEnd : toDate;

  if (effectiveStartDate > yearEnd || effectiveEndDate < yearStart) {
    return 0;
  }

  return calculateWorkingDays(effectiveStartDate, effectiveEndDate);
};

export const getMonthWorkingDays = (month: number, year: number, nonWorkingOfficialHolidays: number): number => {
  const startDate = new Date(year, month, 1);
  const endDate = new Date(year, month + 1, 0);
  return calculateWorkingDays(startDate, endDate) - nonWorkingOfficialHolidays;
};


