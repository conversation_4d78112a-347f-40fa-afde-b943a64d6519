import React, { useEffect, useMemo, useRef, useState } from "react";
import DatesTableView, {
  DayInfo,
} from "../../components/CalendarComponent/DatesTableView";
import { AlignmentPosition } from "../../components/CalendarComponent/types/AlignmentPosition.ts";
import { onPayrollsLoaded, selectPayrolls } from "../payroll/payrollsActions";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { useFilteredEmployees, Employee } from "./useFilteredEmployees";
import { AbsenceInfo } from "../../components/CalendarComponent/types/AbsenceInfo.ts";
import { useCompany } from "../companies/CompanyContext.tsx";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus.ts";
import { calculateLeaveDaysInPeriod } from "../../services/calendar/calendarService.ts";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO.ts";
import { useModal } from "../../components/PopUp/ActionModalContext";
import { translate } from "../../services/language/Translator";
import { useAbsence } from "../absences/AbsenceContext";
import { useMenu } from "../MenuContext";
import {
  selectHolidays,
  onHolidaysLoaded,
} from "../holidays/holidayActions.ts";
import { HolidayType } from "../../models/Enums/Calendar/HolidayType.ts";

interface EmployeeRowPosition {
  [week: number]: { [employeeId: string]: number };
}

interface DatesTableContainerProps {
  selectedPayroll?: LightPayrollDTO;
  setSelectedPayroll: (payroll: LightPayrollDTO | undefined) => void;
  selectedEmployee?: Employee;
  hoveredEmployee?: Employee;
  showMyAbsences: boolean;
  selectedMonth: number;
  selectedYear: number;
  setSelectedMonth: (month: number) => void;
  setSelectedYear: (year: number) => void;
  highlightedAbsenceId?: string;
  isFromNotification: boolean;
}

const DatesTableContainer: React.FC<DatesTableContainerProps> = ({
  selectedPayroll,
  setSelectedPayroll,
  selectedEmployee,
  hoveredEmployee,
  showMyAbsences,
  selectedMonth,
  selectedYear,
  setSelectedMonth,
  setSelectedYear,
  highlightedAbsenceId,
  isFromNotification,
}) => {
  const dispatch = useAppDispatch();
  const payrolls = useAppSelector(selectPayrolls);
  const holidays = useAppSelector(selectHolidays).holidays;
  const { setSelectedAbsence } = useAbsence();
  const { toggleMenu, changeView, isOpen } = useMenu();

  const [employeeRowPositions, setEmployeeRowPositions] =
    useState<EmployeeRowPosition>({});
  const { company } = useCompany();
  const [combinedDays, setCombinedDays] = useState<DayInfo[]>([]);
  const [positionUpdateFlag, setPositionUpdateFlag] = useState<boolean>(false);
  const [maxRowsPerWeek, setMaxRowsPerWeek] = useState<{
    [week: number]: number;
  }>({});
  const { openModal } = useModal();
  const shownApprovedAbsenceIdRef = useRef<string | null>(null);

  const currentDate = new Date().getDate();
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();

  useEffect(() => {
    if (!payrolls.payrolls || payrolls.payrolls.length === 0) {
      dispatch(onPayrollsLoaded(company.id));
    }
  }, [dispatch, company.id]);

  useEffect(() => {
    dispatch(onHolidaysLoaded(selectedYear, selectedMonth + 1));
  }, [dispatch, selectedMonth, selectedYear]);

  const initialNotificationAbsenceIdRef = useRef<string | null>(null);
  useEffect(() => {
    if (
      isFromNotification &&
      initialNotificationAbsenceIdRef.current === null &&
      highlightedAbsenceId
    ) {
      initialNotificationAbsenceIdRef.current = highlightedAbsenceId;
    }
  }, [isFromNotification, highlightedAbsenceId]);

  useEffect(() => {
    if (!highlightedAbsenceId || !isFromNotification) return;
    if (
      initialNotificationAbsenceIdRef.current &&
      highlightedAbsenceId !== initialNotificationAbsenceIdRef.current
    )
      return;

    const leaves = payrolls.payrolls.flatMap((p) => p.leaves);
    const leave = leaves.find((l) => l.id === highlightedAbsenceId);
    if (
      leave &&
      leave.status === AbsenceStatus.Approved &&
      shownApprovedAbsenceIdRef.current !== highlightedAbsenceId
    ) {
      shownApprovedAbsenceIdRef.current = highlightedAbsenceId;
      openModal({
        type: "info",
        title: translate("strSelectedAbsenceAlreadyApproved"),
        confirmLabel: translate("Ok"),
      });
    }
  }, [
    highlightedAbsenceId,
    payrolls.payrolls,
    isFromNotification,
    initialNotificationAbsenceIdRef.current,
  ]);

  useEffect(() => {
    if (!highlightedAbsenceId) return;

    const payroll = payrolls.payrolls.find((p) =>
      p.leaves.some((l) => l.id === highlightedAbsenceId)
    );
    const leave = payroll?.leaves.find((l) => l.id === highlightedAbsenceId);
    if (!payroll || !leave) return;

    const absenceToSelect: AbsenceInfo = {
      id: leave.id ?? "",
      payrollId: leave.payrollId,
      userId: payroll.employee.userId,
      employeeName: `${payroll.employee.firstName || ""} ${
        payroll.employee.lastName || ""
      }`.trim(),
      row: 1,
      positonRounding: AlignmentPosition.Center,
      isHospital: leave.isHospital,
      status: leave.status,
      isHighlighted: true,
      startDate: leave.fromDate,
      endDate: leave.toDate,
      isOverlapping: leave.isOverlapping,
      typeIdentifier: leave.typeIdentifier,
      comment: leave.isHospital ? leave.sickNote ?? "" : leave.reference ?? "",
      sickNote: leave.sickNote ?? "",
      exportStatus: leave.exportStatus,
    };

    setSelectedAbsence(absenceToSelect);
    changeView("absence", "other", {
      selectedYear,
      selectedMonth,
    });

    if (!isOpen) toggleMenu();
  }, [highlightedAbsenceId, payrolls.payrolls, selectedMonth, selectedYear]);

  useEffect(() => {
    if (!highlightedAbsenceId) {
      setSelectedAbsence(null);
    }
  }, [highlightedAbsenceId]);

  const usedLeaveThisMonth = useMemo(() => {
    var leaves =
      selectedEmployee && !showMyAbsences
        ? selectedEmployee.payrolls.flatMap((p) => p.leaves)
        : payrolls.payrolls
            .filter((p) => p.id === selectedPayroll?.workTimeId)
            .flatMap((p) => p.leaves);

    if (leaves.length === 0) return 0;
    const employeeLeaves = leaves.filter(
      (leave) => !leave.isHospital && leave.status === AbsenceStatus.Approved
    );
    const usedDays = employeeLeaves
      ?.map((leave) =>
        calculateLeaveDaysInPeriod(
          leave,
          new Date(selectedYear, selectedMonth, 1),
          new Date(selectedYear, selectedMonth + 1, 0)
        )
      )
      .reduce((acc, curr) => acc + curr, 0);

    return usedDays;
  }, [
    selectedEmployee,
    selectedPayroll,
    selectedMonth,
    selectedYear,
    payrolls.payrolls,
    showMyAbsences,
  ]);

  const usedHospitalLeaveThisMonth = useMemo(() => {
    var leaves =
      selectedEmployee && !showMyAbsences
        ? selectedEmployee.payrolls.flatMap((p) => p.leaves)
        : payrolls.payrolls
            .filter((p) => p.id === selectedPayroll?.workTimeId)
            .flatMap((p) => p.leaves);
    if (leaves.length === 0) return 0;
    const employeeLeaves = leaves.filter(
      (leave) => leave.isHospital && leave.status === AbsenceStatus.Approved
    );

    const usedDays = employeeLeaves
      ?.map((leave) =>
        calculateLeaveDaysInPeriod(
          leave,
          new Date(selectedYear, selectedMonth, 1),
          new Date(selectedYear, selectedMonth + 1, 0)
        )
      )
      .reduce((acc, curr) => acc + curr, 0);

    return usedDays;
  }, [
    selectedEmployee,
    selectedPayroll,
    selectedMonth,
    selectedYear,
    payrolls.payrolls,
    showMyAbsences,
  ]);

  const filteredEmployees = useFilteredEmployees(
    payrolls.payrolls,
    selectedMonth,
    selectedYear,
    selectedPayroll
  );

  useEffect(() => {
    const updatedPositions = fillEmployeeRowPositions(employeeRowPositions);
    const adjustedPositions = adjustPositions(updatedPositions);
    const updatedPositionsForAllDays =
      fillEmployeeRowPositions(adjustedPositions);
    setEmployeeRowPositions(updatedPositionsForAllDays);
    updateMissingEmployeesForAllDays(updatedPositionsForAllDays);
  }, [positionUpdateFlag, filteredEmployees, highlightedAbsenceId]);

  useEffect(() => {
    const newDays = calculateDays(selectedMonth, selectedYear);
    setCombinedDays(newDays);
    setEmployeeRowPositions({});
    setMaxRowsPerWeek({});
    setPositionUpdateFlag(!positionUpdateFlag);
  }, [selectedMonth, selectedYear]);

  const findMonthNumber = (type: string) => {
    let currMonthOffset: number;
    if (type === "prevMonth") currMonthOffset = -1;
    else if (type === "currentMonth") currMonthOffset = 0;
    else currMonthOffset = 1;
    return selectedMonth + currMonthOffset;
  };

  const updateMissingEmployeesForAllDays = (
    adjustedPositions: EmployeeRowPosition
  ) => {
    const updatedDays = combinedDays.map((day) => ({
      ...day,
      missingEmployees: [] as AbsenceInfo[],
    }));

    const allLeaves = filteredEmployees.flatMap((employee) =>
      employee.payrolls.flatMap((p) =>
        p.leaves.map((leave) => ({
          leave,
          employee,
        }))
      )
    );

    const sortedLeaves = [...allLeaves].sort(
      (a, b) =>
        new Date(a.leave.fromDate).getTime() -
        new Date(b.leave.fromDate).getTime()
    );

    const leaveRowAssignments = new Map<string, number>();
    sortedLeaves.forEach(({ leave }) => {
      let assignedRow = 1;

      while (true) {
        const hasConflict = sortedLeaves.some(({ leave: otherLeave }) => {
          if (leave.id === otherLeave.id) return false;

          if (leaveRowAssignments.get(otherLeave.id ?? "") !== assignedRow)
            return false;

          const leaveStart = new Date(leave.fromDate);
          const leaveEnd = new Date(leave.toDate);
          const otherStart = new Date(otherLeave.fromDate);
          const otherEnd = new Date(otherLeave.toDate);

          leaveStart.setHours(0, 0, 0, 0);
          leaveEnd.setHours(0, 0, 0, 0);
          otherStart.setHours(0, 0, 0, 0);
          otherEnd.setHours(0, 0, 0, 0);

          return leaveStart <= otherEnd && leaveEnd >= otherStart;
        });

        if (!hasConflict) {
          leaveRowAssignments.set(leave.id ?? "", assignedRow);
          break;
        }
        assignedRow++;
      }
    });

    const weekMaxRows: { [week: number]: number } = {};

    updatedDays.forEach((dayInfo, index) => {
      const currMonth = findMonthNumber(dayInfo.type);
      const dayDate = new Date(selectedYear, currMonth, dayInfo.dayNumber);
      const weekNumber = Math.floor(index / 7);
      const currentEmployeeRowPosition = adjustedPositions[weekNumber] || {};

      filteredEmployees.forEach((employee, employeeIndex) => {
        if (!(employee.id in currentEmployeeRowPosition)) {
          currentEmployeeRowPosition[employee.id] = employeeIndex * 3;
        }

        const employeeLeaves = employee.payrolls.flatMap((p) => p.leaves);

        employeeLeaves.forEach((leave) => {
          const leaveStart = new Date(leave.fromDate);
          const leaveEnd = new Date(leave.toDate);
          dayDate.setHours(0, 0, 0, 0);
          leaveStart.setHours(0, 0, 0, 0);
          leaveEnd.setHours(0, 0, 0, 0);

          if (dayDate < leaveStart || dayDate > leaveEnd) return;

          const finalRowPosition = leaveRowAssignments.get(leave.id ?? "") ?? 1;

          weekMaxRows[weekNumber] = Math.max(
            weekMaxRows[weekNumber] || 0,
            finalRowPosition + 1
          );

          let positionAlignment: AlignmentPosition = AlignmentPosition.Center;
          if (
            dayDate.getTime() === leaveStart.getTime() &&
            dayDate.getTime() === leaveEnd.getTime()
          ) {
            positionAlignment = AlignmentPosition.Both;
          } else if (dayDate.getTime() === leaveStart.getTime()) {
            positionAlignment = AlignmentPosition.Left;
          } else if (dayDate.getTime() === leaveEnd.getTime()) {
            positionAlignment = AlignmentPosition.Right;
          }

          dayInfo.missingEmployees = [
            ...dayInfo.missingEmployees,
            {
              id: leave.id ?? "",
              payrollId: leave.payrollId,
              userId: employee.userId,
              employeeName: employee.name,
              row: finalRowPosition,
              positonRounding: positionAlignment,
              isHospital: leave.isHospital,
              status: leave.status,
              exportStatus: leave.exportStatus,
              isHighlighted: highlightedAbsenceId
                ? leave.id === highlightedAbsenceId
                : false,
              startDate: leave.fromDate,
              endDate: leave.toDate,
              isOverlapping: leave.isOverlapping,
              typeIdentifier: leave.typeIdentifier,
              comment: leave.isHospital
                ? leave.sickNote ?? ""
                : leave.reference ?? "",
              sickNote: leave.sickNote ?? "",
            },
          ];
        });
      });
    });

    setMaxRowsPerWeek(weekMaxRows);
    setCombinedDays(updatedDays);
  };

  const fillEmployeeRowPositions = (adjustedPositions: EmployeeRowPosition) => {
    combinedDays.forEach((dayInfo, index) => {
      const currMonth = findMonthNumber(dayInfo.type);
      const dayDate = new Date(selectedYear, currMonth, dayInfo.dayNumber);
      const weekNumber = Math.floor(index / 7);
      const currentEmployeeRowPosition = adjustedPositions[weekNumber] || {};
      adjustedPositions[weekNumber] = currentEmployeeRowPosition;

      filteredEmployees.forEach((employee) => {
        if (isEmployeeMissingOnDay(employee, dayDate)) {
          if (!(employee.id in currentEmployeeRowPosition)) {
            adjustedPositions[weekNumber][employee.id] =
              updateRowPositionsForWeek(
                weekNumber,
                employee.id,
                adjustedPositions
              );
          }
        }
      });
    });
    return adjustedPositions;
  };

  const adjustPositions = (updatedEmployees: EmployeeRowPosition) => {
    Object.entries(updatedEmployees).forEach(([week, positions]) => {
      const sortedEntries = Object.entries(positions).sort(
        (a, b) => (a[1] as unknown as number) - (b[1] as unknown as number)
      );
      updatedEmployees[Number(week)] = sortedEntries.reduce<{
        [key: string]: number;
      }>((acc, [employee], index) => {
        acc[employee] = index + 1;
        return acc;
      }, {});
    });
    return updatedEmployees;
  };

  const isEmployeeMissingOnDay = (
    employee: Employee,
    dayDate: Date
  ): boolean => {
    const employeeLeaves = employee.payrolls.flatMap((p) => p.leaves);
    return employeeLeaves.some((leave) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);
      return dayDate >= leaveStart && dayDate <= leaveEnd;
    });
  };

  const updateRowPositionsForWeek = (
    weekNumber: number,
    newEmployee: string,
    adjustedPositions: EmployeeRowPosition
  ) => {
    const weekPositions = adjustedPositions[weekNumber] || {};
    const previousWeekPositions = adjustedPositions[weekNumber - 1] || {};
    return (
      previousWeekPositions[newEmployee] ||
      Math.max(0, ...Object.values(weekPositions)) + 1
    );
  };

  const createDayInfo = (dayNumber: number, type: string) => {
    const currMonth = findMonthNumber(type);
    const dayDate = new Date(selectedYear, currMonth, dayNumber);
    return {
      dayDate,
      dayNumber,
      type,
      missingEmployees: [] as AbsenceInfo[],
    };
  };

  const calculateDays = (selectedMonth: number, selectedYear: number) => {
    const daysInMonth = (month: number, year: number) =>
      new Date(year, month + 1, 0).getDate();

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1).getDay();
    const numDaysCurrentMonth = daysInMonth(selectedMonth, selectedYear);
    const adjustDayIndex = (dayIndex: number) =>
      dayIndex === 0 ? 7 : dayIndex;
    const prevMonthDisplayDays = adjustDayIndex(firstDayOfMonth) - 1;

    const prevMonthDays = Array.from(
      { length: prevMonthDisplayDays },
      (_, i) =>
        daysInMonth(selectedMonth - 1, selectedYear) -
        prevMonthDisplayDays +
        i +
        1
    );

    const currentMonthDays = Array.from(
      { length: numDaysCurrentMonth },
      (_, i) => i + 1
    );

    const totalDays = prevMonthDisplayDays + numDaysCurrentMonth;
    const nextMonthDisplayDays = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);

    const nextMonthDays = Array.from(
      { length: nextMonthDisplayDays },
      (_, i) => i + 1
    );

    return [
      ...prevMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "prevMonth")
      ),
      ...currentMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "currentMonth")
      ),
      ...nextMonthDays.map((dayNumber: number) =>
        createDayInfo(dayNumber, "nextMonth")
      ),
    ] as DayInfo[];
  };

  const goToPrevMonth = () => {
    let newMonth = selectedMonth - 1;
    let newYear = selectedYear;
    if (newMonth < 0) {
      newMonth = 11;
      newYear -= 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToNextMonth = () => {
    let newMonth = selectedMonth + 1;
    let newYear = selectedYear;
    if (newMonth > 11) {
      newMonth = 0;
      newYear += 1;
    }
    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const isCurrentDay = (day: number) => {
    return (
      selectedMonth === currentMonth &&
      selectedYear === currentYear &&
      day === currentDate
    );
  };

  const isHoliday = (day: number) => {
    if (!holidays) return false;

    const dateStr = `${selectedYear}-${String(selectedMonth + 1).padStart(
      2,
      "0"
    )}-${String(day).padStart(2, "0")}`;

    return holidays.some((holiday) => holiday.date === dateStr);
  };

  const calculateOfficialHolidaysNumber = () => {
    return holidays.filter((holiday) => holiday.type !== HolidayType.DayOff)
      .length;
  };

  const calculateNonWorkingDays = () => {
    return holidays.filter((holiday) => !holiday.isWeekend).length;
  };

  return (
    <DatesTableView
      data-testid="dates-table-view"
      selectedMonth={selectedMonth}
      selectedYear={selectedYear}
      usedLeavesThisMonth={usedLeaveThisMonth ?? 0}
      usedHospitalLeavesThisMonth={usedHospitalLeaveThisMonth ?? 0}
      days={combinedDays}
      maxRowsPerWeek={maxRowsPerWeek}
      onPrevMonth={goToPrevMonth}
      onNextMonth={goToNextMonth}
      isCurrentDay={isCurrentDay}
      isHoliday={isHoliday}
      officialHolidaysNumber={calculateOfficialHolidaysNumber()}
      nonWorkingOfficialHolidays={calculateNonWorkingDays()}
      setSelectedPayroll={setSelectedPayroll}
      selectedPayroll={selectedPayroll}
      selectedEmployee={selectedEmployee}
      hoveredEmployee={hoveredEmployee}
      showMyAbsences={showMyAbsences}
    />
  );
};

export default DatesTableContainer;
