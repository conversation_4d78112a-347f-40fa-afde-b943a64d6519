import { Action, Reducer } from "redux";
import { AppThunk, RootState, ClearStateAction } from "../../app/store";
import { authenticatedGet } from "../../services/worktimeConnectionService";
import { HolidayDTO } from "../../models/DTOs/Holidays/HolidaysDTO";

interface HolidaysState {
  holidays: HolidayDTO[];
}

interface GetHolidaysAction {
    type: "GET_HOLIDAYS";
    holidays: HolidayDTO[];
  }

  type KnownActions = GetHolidaysAction | ClearStateAction;

  const getHolidaysAction = (holidays: HolidayDTO[]): GetHolidaysAction => ({
    type: "GET_HOLIDAYS",
    holidays,
  });

  export const actionCreators = {
    onHolidaysLoaded: (year: number, month: number): AppThunk<void, KnownActions> => {
      return async (dispatch: any) => {
          try {
              const holidays = await authenticatedGet<HolidayDTO[]>(`holidays?year=${year}&month=${month}`);
              dispatch(getHolidaysAction(holidays)); 
              } catch (error) {
                console.error("Failed to load holidays:", error);
            }
        };
    }
  }

  export const {
    onHolidaysLoaded
} = actionCreators;

const initialState: HolidaysState = {
    holidays: [],
  };

  export const reducer: Reducer<HolidaysState> = (
    state = initialState,
    action: Action
  ) => {
    const incomingAction = action as KnownActions;
    switch (incomingAction.type) {
      case "GET_HOLIDAYS":
        return {
          ...state,
          holidays: incomingAction.holidays,
        };
      case "CLEAR_STATE":
        return initialState;
      default:
        return state;
    }
  };

  export const selectHolidays = (state: RootState) => state.holidays;