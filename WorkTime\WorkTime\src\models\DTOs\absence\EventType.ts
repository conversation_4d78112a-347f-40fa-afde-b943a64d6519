export enum EventType {
  ПлатенГодишенОтпуск = 101,
  ПлатенГодишенОтпускЗаМиналаГодина = 102,
  НеплатенСОсигурителенСтажОтОсигурител = 103,
  НеплатенСОсигурителенСтажОтОсигурен = 104,
  НеплатенЗаДетеДо8 = 105,
  НеплатенБезСтажОтОсигурител = 106,
  НеплатенБезСтажОтОсигурен = 107,
  Самоотлъчване = 108,
  ГледанеНаБоленЧлен = 110,
  ТрудоваЗлополука = 111,
  ПрофесионалнаБолест = 112,
  НеплатенПоНетрудоспособност = 113,
  НеплатенЗаБременност = 116,
  ОтпускМайка135До410 = 117,
  ОтпускБащаНад6Месеца = 118,
  Отпуск15ДниРаждане = 119,
  ОтпускЗаДетеДо2 = 120,
  ПлатенПоДругиЧленове = 121,
  ОтпускОсиновяванеДо5 = 122,
  НеплатенОсиновяванеДо5 = 123,
  БащаОсиновителДо5 = 124,
  ПлатенПоЧл173а = 125,
  БащаГледаДетеДо8 = 126,

  // Платен отпуск по други членове (обучение) 200 range
  ПлатенОбучениеЧл169Ал1 = 201,
  ПлатенДържавенИзпитЧл169Ал3 = 202,
  ПлатенНаучнаСтепенЧл169Ал4 = 203,
  ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1 = 204,
  ПлатенКандидатстванеУниверситетЧл170Ал1Т2 = 205,
  ПлатенСлужебенТворческиЧл161Ал1 = 206,

  // Инцидентен отпуск 300 range
  ИнцидентенГражданскиБракЧл157Ал1Т1 = 301,
  ИнцидентенКръводаряванеЧл157Ал1Т2 = 302,
  ИнцидентенСмъртРоднинаЧл157Ал1Т3 = 303,
  ИнцидентенЯвяванеВСъдаЧл157Ал1Т4 = 304,
  ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5 = 305,
  ИнцидентенДоброволецПриБедствияЧл157Ал1Т7 = 306,
  ИнцидентенПрегледБременностИнВитроЧл157Ал2 = 307,
  ИнцидентенСлужбаВРезервЧл158Ал1 = 308,
  ИнцидентенДруг = 309,

  // Неплатени подвидове 400 range (освен базовите с/без стаж и за дете до 8 и самоотлъчване)
  НеплатенКандидатстванеСредноУчилищеЧл170Ал2 = 401,
  НеплатенКандидатстванеУниверситетЧл170Ал2 = 402,
  НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1 = 403,
  НеплатенДържавенИзпитДиплСУЧл171Ал1Т2 = 404,
  НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3 = 405,
  НеплатенДисертацияЧл171Ал1Т4 = 406,
  НеплатенСлужебенТворческиЧл161Ал1 = 407,

  // Компенсация (към платен по други членове) 500 range
  ПлатенКомпенсация = 501,

  // Болничен 600 range
  Болничен = 601,
  БолниченПоБременност = 602,
  БолниченСледРаждане = 603,
}

export const EventTypeDescriptions: Record<EventType, string> = {
  [EventType.ПлатенГодишенОтпуск]: "Платен годишен отпуск",
  [EventType.ПлатенГодишенОтпускЗаМиналаГодина]:
    "Платен годишен отпуск за минала година",
  [EventType.НеплатенСОсигурителенСтажОтОсигурител]:
    "Неплатен отпуск с осигурителен стаж от осигурител",
  [EventType.НеплатенСОсигурителенСтажОтОсигурен]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенЗаДетеДо8]:
    "Неплатен отпуск за отглеждане на дете до 8 години",
  [EventType.НеплатенБезСтажОтОсигурител]:
    "Неплатен отпуск без осигурителен стаж от осигурител",
  [EventType.НеплатенБезСтажОтОсигурен]:
    "Неплатен отпуск без осигурителен стаж от осигурен",
  [EventType.Самоотлъчване]: "Самоотлъчване",
  [EventType.Болничен]: "Болничен",
  [EventType.ГледанеНаБоленЧлен]: "Гледане на болен член от семейството",
  [EventType.ТрудоваЗлополука]: "Трудова злополука",
  [EventType.ПрофесионалнаБолест]: "Професионална болест",
  [EventType.НеплатенПоНетрудоспособност]:
    "Неплатен отпуск за временна нетрудоспособност",
  [EventType.БолниченПоБременност]: "Болничен по бременност",
  [EventType.БолниченСледРаждане]: "Болничен след раждане",
  [EventType.НеплатенЗаБременност]: "Неплатен отпуск за бременност и раждане",
  [EventType.ОтпускМайка135До410]: "Отпуск за майка след раждане от 135 до 410",
  [EventType.ОтпускБащаНад6Месеца]:
    "Отпуск за баща за гледане на дете над 6 месеца",
  [EventType.Отпуск15ДниРаждане]: "Отпуск до 15 дни при раждане на дете",
  [EventType.ОтпускЗаДетеДо2]: "Отпуск за отглеждане на дете до 2 години",
  [EventType.ПлатенПоДругиЧленове]: "Платен отпуск по други чл от КТ",
  [EventType.ОтпускОсиновяванеДо5]:
    "Отпуск при осиновяване на дете до 5-годишна възраст",
  [EventType.НеплатенОсиновяванеДо5]:
    "Неплатен отпуск при осиновяване на дете до 5-годишна възраст",
  [EventType.БащаОсиновителДо5]:
    "Отпуск за баща при осиновяване на дете до 5-годишна възраст",
  [EventType.ПлатенПоЧл173а]: "Платен отпуск по чл. 173а, ал. 1 от КТ",
  [EventType.БащаГледаДетеДо8]:
    "Отпуск за отглеждане на дете до 8-годишна възраст от бащата (осиновителя)",

  // Платен по други членове (обучение) 200 range
  [EventType.ПлатенОбучениеЧл169Ал1]: "Платен отпуск по други чл от КТ",
  [EventType.ПлатенДържавенИзпитЧл169Ал3]: "Платен отпуск по други чл от КТ",
  [EventType.ПлатенНаучнаСтепенЧл169Ал4]: "Платен отпуск по други чл от КТ",
  [EventType.ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1]:
    "Платен отпуск по други чл от КТ",
  [EventType.ПлатенКандидатстванеУниверситетЧл170Ал1Т2]:
    "Платен отпуск по други чл от КТ",
  [EventType.ПлатенСлужебенТворческиЧл161Ал1]:
    "Платен отпуск по други чл от КТ",
  // Инцидентен отпуск 300 range
  [EventType.ИнцидентенГражданскиБракЧл157Ал1Т1]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенКръводаряванеЧл157Ал1Т2]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенСмъртРоднинаЧл157Ал1Т3]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенЯвяванеВСъдаЧл157Ал1Т4]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенДоброволецПриБедствияЧл157Ал1Т7]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенПрегледБременностИнВитроЧл157Ал2]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенСлужбаВРезервЧл158Ал1]:
    "Платен отпуск по други чл от КТ",
  [EventType.ИнцидентенДруг]: "Платен отпуск по други чл от КТ",
  // Компенсация 200 range
  [EventType.ПлатенКомпенсация]: "Платен отпуск по други чл от КТ",

  // Неплатени подвидове 400 range
  [EventType.НеплатенКандидатстванеСредноУчилищеЧл170Ал2]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенКандидатстванеУниверситетЧл170Ал2]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенДържавенИзпитДиплСУЧл171Ал1Т2]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенДисертацияЧл171Ал1Т4]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
  [EventType.НеплатенСлужебенТворческиЧл161Ал1]:
    "Неплатен отпуск с осигурителен стаж от осигурен",
};
