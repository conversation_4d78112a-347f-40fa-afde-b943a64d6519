import { styled } from "styled-components";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import Datepicker from "../../../components/Datepicker/Datepicker";
import Combobox from "../../../components/Combobox/Combobox";
import TwoLineCombobox, {
  TwoLineOption,
} from "../../../components/Combobox/TwoLineCombobox";
import Textbox from "../../../components/Inputs/Textbox";
import noImage from "../../../assets/images/attendancies/no-image.svg";
import noImageHover from "../../../assets/images/attendancies/no-image-hover.svg";
import Button from "../../../components/Inputs/Button";
import { AbsenceInfo } from "../../../components/CalendarComponent/types/AbsenceInfo";
import { useEffect, useState } from "react";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { useAbsenceActions } from "../../../utils/absenceActions";
import { translate } from "../../../services/language/Translator";

const Row = styled(Container)`
  justify-content: center;
  width: 100%;
`;

const ImageUpload = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const AbsenceDetails = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 9rem;
  margin-left: 2em;
  width: 100%;
  position: relative;
  z-index: 1;
`;

const UploadImage = styled(Image)`
  cursor: pointer;

  &:hover {
    content: url(${noImageHover});
  }
`;

const ButtonGroup = styled(Container)`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const ActionButton = styled(Button)`
  flex: 1;
`;

const RequestButton = styled(Button)`
  margin-top: 1rem;
  width: 100%;
`;

export interface FormData {
  startDate: string | null;
  endDate: string | null;
  selectedOption: string;
  comment: string;
  selectedSubOptionId?: string | null;
  eventType?: number | null;
}

interface AbsenceFormProps {
  options: string[];
  isLoading: boolean;
  selectedAbsence: AbsenceInfo | null;
  absencesVisible: boolean;
  initialData?: Partial<FormData>;
  onSubmit: (formData: FormData) => void;
  onEdit: (formData: FormData) => void;
  isEditing: boolean;
  isAdmin: boolean;
  initialMonth?: number;
  initialYear?: number;
  editActivationToggle?: boolean;
}

const formatDate = (date: Date) => {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
};

const parseDateString = (dateStr: string): Date => {
  const [day, month, year] = dateStr.split(".");
  return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day)));
};

const AbsenceForm = ({
  options,
  isLoading,
  selectedAbsence,
  absencesVisible,
  initialData,
  onSubmit,
  onEdit,
  isEditing,
  isAdmin,
  initialMonth,
  initialYear,
  editActivationToggle,
}: AbsenceFormProps) => {
  type SubOption = TwoLineOption & { eventType: number };
  interface Category {
    key: string;
    eventType?: number;
    subOptions?: SubOption[];
  }

  const categories: Category[] = [
    { key: "Paid Leave", eventType: 101 },
    {
      key: "Paid Leave for Training",
      subOptions: [
        {
          id: "training_169_1",
          title: "Training",
          subtitle: "Art. 169, para. 1 of the LC",
          eventType: 201,
        },
        {
          id: "training_169_3",
          title: "State exam/thesis",
          subtitle: "Art. 169, para. 3 of the LC",
          eventType: 202,
        },
        {
          id: "training_169_4",
          title: "Obtaining a scientific degree",
          subtitle: "Art. 169, para. 4 of the LC",
          eventType: 203,
        },
        {
          id: "training_170_1_1",
          title: "Applying to secondary school",
          subtitle: "Art. 170, para. 1, item 1 of the LC",
          eventType: 204,
        },
        {
          id: "training_170_1_2",
          title: "Applying to university",
          subtitle: "Art. 170, para. 1, item 2 of the LC",
          eventType: 205,
        },
        {
          id: "training_161_1",
          title: "Official/creative",
          subtitle: "Art. 161, para. 1 of the LC",
          eventType: 206,
        },
      ],
    },
    {
      key: "Incidental Leave",
      subOptions: [
        {
          id: "incident_157_1_1",
          title: "Civil marriage",
          subtitle: "Art. 157, para. 1, item 1 of the LC",
          eventType: 301,
        },
        {
          id: "incident_157_1_2",
          title: "Blood donation",
          subtitle: "Art. 157, para. 1, item 2 of the LC",
          eventType: 302,
        },
        {
          id: "incident_157_1_3",
          title: "Death of a relative",
          subtitle: "Art. 157, para. 1, item 3 of the LC",
          eventType: 303,
        },
        {
          id: "incident_157_1_4",
          title: "Court appearance",
          subtitle: "Art. 157, para. 1, item 4 of the LC",
          eventType: 304,
        },
        {
          id: "incident_157_1_5",
          title: "Participation in meetings",
          subtitle: "Art. 157, para. 1, item 5/5a of the LC",
          eventType: 305,
        },
        {
          id: "incident_157_1_7",
          title: "Volunteer in disasters",
          subtitle: "Art. 157, para. 1, item 7 of the LC",
          eventType: 306,
        },
        {
          id: "incident_157_2",
          title: "Pregnancy/IVF check-up",
          subtitle: "Art. 157, para. 2 of the LC",
          eventType: 307,
        },
        {
          id: "incident_158_1",
          title: "Reserve service",
          subtitle: "Art. 158, para. 1",
          eventType: 308,
        },
        { id: "incident_other", title: "Other", eventType: 309 },
      ],
    },
    {
      key: "Unpaid Leave",
      subOptions: [
        {
          id: "unpaid_with_service_employer",
          title: "With insurance service (employer)",
          eventType: 103,
        },
        {
          id: "unpaid_with_service_employee",
          title: "With insurance service (insured)",
          eventType: 104,
        },
        {
          id: "unpaid_without_service_employer",
          title: "Without insurance service (employer)",
          eventType: 106,
        },
        {
          id: "unpaid_without_service_employee",
          title: "Without insurance service (insured)",
          eventType: 107,
        },
        {
          id: "unpaid_child_167a",
          title: "Childcare up to 8 years",
          subtitle: "Art. 167a of the LC",
          eventType: 105,
        },
        {
          id: "unpaid_170_2_school",
          title: "Applying to secondary school",
          subtitle: "Art. 170, para. 2 of the LC",
          eventType: 401,
        },
        {
          id: "unpaid_170_2_university",
          title: "Applying to university",
          subtitle: "Art. 170, para. 2 of the LC",
          eventType: 402,
        },
        {
          id: "unpaid_171_1_1",
          title: "Preparation and taking an exam",
          subtitle: "Art. 171, para. 1, item 1 of the LC",
          eventType: 403,
        },
        {
          id: "unpaid_171_1_2_su",
          title: "State exam/thesis SU",
          subtitle: "Art. 171, para. 1, item 2 of the LC",
          eventType: 404,
        },
        {
          id: "unpaid_171_1_3_vuz",
          title: "State exam/thesis University",
          subtitle: "Art. 171, para. 1, item 3 of the LC",
          eventType: 405,
        },
        {
          id: "unpaid_171_1_4",
          title: "Dissertation",
          subtitle: "Art. 171, para. 1, item 4 of the LC",
          eventType: 406,
        },
        {
          id: "unpaid_creative_161_1",
          title: "Official/creative leave",
          subtitle: "Art. 161, para. 1 of the LC",
          eventType: 407,
        },
        {
          id: "unpaid_samoootlachvane",
          title: "Absence without leave",
          eventType: 108,
        },
      ],
    },
    { key: "Compensation", eventType: 501 },
  ];

  const getCategory = (key: string) => categories.find((c) => c.key === key);
  const findByEventType = (
    eventType: number
  ): { category?: Category; subId?: string | null } => {
    for (const cat of categories) {
      if (cat.eventType === eventType) return { category: cat, subId: null };
      if (cat.subOptions) {
        const match = cat.subOptions.find((o) => o.eventType === eventType);
        if (match) return { category: cat, subId: match.id };
      }
    }
    return {};
  };

  const initialDerived = (() => {
    if (selectedAbsence) {
      const typeId = Number(selectedAbsence.typeIdentifier);
      if (selectedAbsence.isHospital) {
        return {
          option: typeId === 602 ? "Maternity Leave" : "Sick Leave",
          subId: null as string | null,
          start: formatDate(new Date(selectedAbsence.startDate)),
          end: formatDate(new Date(selectedAbsence.endDate)),
          comm: selectedAbsence.isHospital
            ? selectedAbsence.sickNote || ""
            : selectedAbsence.comment || "",
        };
      }
      const found = findByEventType(typeId);
      return {
        option: found.category?.key ?? options[0],
        subId: found.subId ?? null,
        start: formatDate(new Date(selectedAbsence.startDate)),
        end: formatDate(new Date(selectedAbsence.endDate)),
        comm: selectedAbsence.comment || "",
      };
    }
    return {
      option: initialData?.selectedOption || options[0],
      subId: null as string | null,
      start: initialData?.startDate || null,
      end: initialData?.endDate || null,
      comm: initialData?.comment || "",
    };
  })();

  const [startDate, setStartDate] = useState<string | null>(
    initialDerived.start
  );
  const [endDate, setEndDate] = useState<string | null>(initialDerived.end);
  const [selectedOption, setSelectedOption] = useState<string>(
    initialDerived.option
  );
  const [comment, setComment] = useState<string>(initialDerived.comm);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedSubOptionId, setSelectedSubOptionId] = useState<string | null>(
    initialDerived.subId
  );

  const isAbsenceActive = (absence: AbsenceInfo | null): boolean => {
    if (!absence) return false;
    if (absence.status === AbsenceStatus.Pending) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const absenceStartDate = new Date(absence.startDate);
    absenceStartDate.setHours(0, 0, 0, 0);

    const absenceEndDate = new Date(absence.endDate);
    absenceEndDate.setHours(0, 0, 0, 0);

    return today >= absenceStartDate && today <= absenceEndDate;
  };

  const { handleDeleteAbsence, handleApproveAbsence, handleDeclineAbsence } =
    useAbsenceActions();

  useEffect(() => {
    if (selectedAbsence) {
      const fromDate = formatDate(new Date(selectedAbsence.startDate));
      const toDate = formatDate(new Date(selectedAbsence.endDate));
      setStartDate(fromDate);
      setEndDate(toDate);
      setComment(
        selectedAbsence.isHospital
          ? selectedAbsence.sickNote || ""
          : selectedAbsence.comment || ""
      );

      const typeId = Number(selectedAbsence.typeIdentifier);
      setSelectedSubOptionId(null);

      if (selectedAbsence.isHospital) {
        setSelectedOption(typeId === 602 ? "Maternity Leave" : "Sick Leave");
      } else {
        const found = findByEventType(typeId);
        if (found.category) {
          setSelectedOption(found.category.key);
          setSelectedSubOptionId(found.subId ?? null);
        } else {
          setSelectedOption("Unpaid Leave");
        }
      }
      setHasChanges(false);
    } else {
      setStartDate(initialData?.startDate || null);
      setEndDate(initialData?.endDate || null);
      setSelectedOption(initialData?.selectedOption || options[0]);
      setComment(initialData?.comment || "");
      setSelectedSubOptionId(null);
      setHasChanges(false);
    }
  }, [selectedAbsence, initialData]);

  useEffect(() => {
    if (!selectedAbsence && options.length > 0) {
      setSelectedOption((prev) => prev ?? options[0]);
    }
  }, [options]);

  useEffect(() => {
    if (editActivationToggle !== undefined) {
      setHasChanges(true);
    }
  }, [editActivationToggle]);

  const handleStartDateSelect = (date: Date) => {
    setStartDate(formatDate(date));
    setHasChanges(true);
  };

  const handleEndDateSelect = (date: Date) => {
    setEndDate(formatDate(date));
    setHasChanges(true);
  };

  const handleSelectedChange = (newSelectedOption: string) => {
    setSelectedOption(newSelectedOption);
    const cat = getCategory(newSelectedOption);
    if (cat && cat.subOptions && cat.subOptions.length > 0) {
      setSelectedSubOptionId(cat.subOptions[0].id);
    } else {
      setSelectedSubOptionId(null);
    }
    setHasChanges(true);
  };

  const handleSelectedSubChange = (option: TwoLineOption) => {
    setSelectedSubOptionId(option.id);
    setHasChanges(true);
  };

  const handleCommentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
    setHasChanges(true);
  };

  const deriveEventTypeFromSelection = (): number | null => {
    if (!absencesVisible) {
      return selectedOption === "Maternity Leave" ? 602 : 601;
    }
    const category = getCategory(selectedOption);
    if (!category) return null;
    if (category.subOptions) {
      const effectiveSubId =
        selectedSubOptionId ??
        (category.subOptions.length > 0 ? category.subOptions[0].id : null);
      const sub = category.subOptions.find((s) => s.id === effectiveSubId);
      return sub?.eventType ?? null;
    }
    return category.eventType ?? null;
  };

  const handleSubmit = (e: React.FormEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const formData: FormData = {
      startDate,
      endDate,
      selectedOption,
      comment,
      selectedSubOptionId,
      eventType: deriveEventTypeFromSelection(),
    };
    onSubmit(formData);
    setHasChanges(false);
  };

  const handleEdit = () => {
    const formData: FormData = {
      startDate,
      endDate,
      selectedOption,
      comment,
      selectedSubOptionId,
      eventType: deriveEventTypeFromSelection(),
    };
    onEdit(formData);
    setHasChanges(false);
  };

  return (
    <>
      <Row data-testid="absence-row">
        <Container
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            width: "100%",
          }}
        >
          <ImageUpload data-testid="image-upload-container">
            <UploadImage
              data-testid="no-image"
              size="large"
              alt="no image"
              src={noImage}
            />
          </ImageUpload>
          <AbsenceDetails data-testid="absence-details">
            <Datepicker
              data-testid="start-date-picker"
              initialDate={startDate ? parseDateString(startDate) : null}
              initialMonth={initialMonth}
              initialYear={initialYear}
              onSelectDate={handleStartDateSelect}
              label="Start Date"
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
            <Datepicker
              data-testid="end-date-picker"
              initialDate={endDate ? parseDateString(endDate) : null}
              initialMonth={initialMonth}
              initialYear={initialYear}
              onSelectDate={handleEndDateSelect}
              label="End Date"
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
            <Combobox
              key={`main-${selectedOption}-${absencesVisible}`}
              data-testid="absence-type-combobox"
              options={options}
              initialSelectedItem={selectedOption}
              onChange={handleSelectedChange}
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
            {absencesVisible && getCategory(selectedOption)?.subOptions && (
              <TwoLineCombobox
                key={`sub-${selectedOption}-${selectedSubOptionId ?? "none"}`}
                options={getCategory(selectedOption)!.subOptions!.map((o) => ({
                  id: o.id,
                  title: o.title,
                  subtitle: o.subtitle,
                }))}
                onChange={handleSelectedSubChange}
                initialSelectedId={selectedSubOptionId ?? undefined}
                disabled={!isEditing && selectedAbsence !== null && isAdmin}
                placeholderWhenOpen={translate("Select subtype")}
              />
            )}
          </AbsenceDetails>
        </Container>
        <Textbox
          data-testid="comment-input"
          label="Comment"
          value={comment}
          handleChange={handleCommentChange}
          readOnly={!isEditing && selectedAbsence !== null && isAdmin}
        />
      </Row>
      {selectedAbsence ? (
        <ButtonGroup>
          <ActionButton
            label={!isEditing && isAdmin ? "Cancel" : "Edit"}
            onClick={() =>
              !isEditing && isAdmin
                ? handleDeclineAbsence(selectedAbsence)
                : handleEdit()
            }
            disabled={
              isLoading ||
              (!hasChanges && isEditing) ||
              (!isEditing && isAdmin && selectedAbsence?.isHospital)
            }
          />
          <ActionButton
            label={!isEditing && isAdmin ? "Approve" : "Delete"}
            onClick={() =>
              !isEditing && isAdmin
                ? handleApproveAbsence(selectedAbsence)
                : handleDeleteAbsence(selectedAbsence)
            }
            disabled={
              isLoading || (!isAdmin && isAbsenceActive(selectedAbsence))
            }
          />
        </ButtonGroup>
      ) : (
        <RequestButton
          data-testid="request-button"
          label={absencesVisible ? "Request absence" : "Send sick leave"}
          onClick={handleSubmit}
          disabled={
            startDate === null ||
            endDate === null ||
            isLoading ||
            (startDate !== null &&
              endDate !== null &&
              parseDateString(endDate) < parseDateString(startDate))
          }
        />
      )}
    </>
  );
};

export default AbsenceForm;
