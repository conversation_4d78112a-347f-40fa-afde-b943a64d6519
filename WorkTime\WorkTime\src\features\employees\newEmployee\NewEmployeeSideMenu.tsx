import { useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../../app/hooks";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { useModal } from "../../../components/PopUp/ActionModalContext";
import {
  LOCAL_STORAGE_COMPANY_ID,
  LOCAL_STORAGE_NEW_EMPLOYEE_DATA,
} from "../../../constants/local-storage-constants";
import { EmployeeDTO } from "../../../models/DTOs/employees/EmployeeDTO";
import { IdentityCardDataDTO } from "../../../models/DTOs/newEmployee/IdentityCardDataDTO";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import { NewEmployeeDTO } from "../../../models/DTOs/newEmployee/NewEmployeeDTO";
import {
  initialNewEmployeeFormData,
  NewEmployeeFormData,
} from "../../../models/DTOs/newEmployee/NewEmployeeFormData";
import { PersonalDataDTO } from "../../../models/DTOs/newEmployee/PersonalDataDTO";
import {
  addNewEmployeePayroll,
  doesEmployeeExistInCompany,
} from "../../../services/employees/employeesService";
import { translate } from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";
import { onEmployeePayrollUpdated } from "../../payroll/employeePayrollActions";
import NewAddresses from "./NewAddress";
import NewIdentificationCardData from "./NewIdentificationCardData";
import NewPayroll from "./NewPayroll";
import NewPersonalData from "./NewPersonalData";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
  margin-bottom: 7rem;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ $isSelected: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem 0.8rem 1.8rem;
  text-align: left;
  background-color: ${(p) =>
    p.$isSelected
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.$isSelected
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.$isSelected ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-top: 1rem;
`;

const NavigationContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
  justify-content: center;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 5;
`;

const NextButton = styled(Button)`
  width: 90%;
`;

const NewEmployeeSideMenu = () => {
  const dispatch = useAppDispatch();
  const { toggleMenu, changeView } = useMenu();
  const { openModal } = useModal();
  const [activeStep, setActiveStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState<NewEmployeeFormData>(
    JSON.parse(
      localStorage.getItem(LOCAL_STORAGE_NEW_EMPLOYEE_DATA) ?? "null"
    ) ?? initialNewEmployeeFormData
  );

  const isCurrentStepValid = () => {
    localStorage.setItem(
      LOCAL_STORAGE_NEW_EMPLOYEE_DATA,
      JSON.stringify(formData)
    );

    switch (activeStep) {
      case 1:
        return formData.personalData.isValid;
      case 2:
        return (
          formData.identityCardData.isValid && formData.personalData.isValid
        );
      case 3:
        return (
          formData.addressData.isValid &&
          formData.identityCardData.isValid &&
          formData.personalData.isValid
        );
      case 4:
        return (
          (formData.payrollData?.isValid || false) &&
          formData.addressData.isValid &&
          formData.identityCardData.isValid &&
          formData.personalData.isValid
        );
      default:
        return false;
    }
  };

  const handlePersonalDataValidation = (
    isValid: boolean,
    data: PersonalDataDTO
  ) => {
    setFormData({
      ...formData,
      personalData: { isValid, data },
    });
  };

  const handleIdentificationDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      identityCardData: { isValid, data },
    });
  };

  const handleAddressDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      addressData: { isValid, data },
    });
  };

  const handlePayrollDataValidation = (isValid: boolean, data: any) => {
    setFormData({
      ...formData,
      payrollData: { isValid, data },
    });
  };

  const steps = [
    {
      id: 1,
      label: translate("str1PersonalData"),
      component: (
        <NewPersonalData
          onValidation={handlePersonalDataValidation}
          data={formData.personalData.data}
        />
      ),
    },
    {
      id: 2,
      label: translate("str2IDCardData"),
      component: (
        <NewIdentificationCardData
          onValidation={handleIdentificationDataValidation}
          data={formData.identityCardData.data}
        />
      ),
    },
    {
      id: 3,
      label: translate("str3Addresses"),
      component: (
        <NewAddresses
          onValidation={handleAddressDataValidation}
          data={formData.addressData.data}
          isNewEmployeeFlow={true}
        />
      ),
    },
    {
      id: 4,
      label: translate("str4Payroll"),
      component: (
        <NewPayroll
          onValidation={handlePayrollDataValidation}
          data={formData.payrollData?.data}
        />
      ),
    },
  ];

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId);
  };

  const isStepDisabled = (stepId: number) => {
    return stepId > activeStep;
  };

  const handleNextClick = () => {
    if (activeStep < steps.length && isCurrentStepValid()) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleSubmit = async () => {
    if (!isCurrentStepValid()) {
      return;
    }

    setIsLoading(true);
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    const existingEmployee = (await doesEmployeeExistInCompany(
      formData.personalData.data.email,
      formData.personalData.data.egn,
      companyId
    )) as EmployeeDTO;

    if (existingEmployee !== undefined) {
      openModal({
        type: "info",
        title:
          existingEmployee.email && formData.personalData.data.email
            ? translate("strEmployeeEmailExists").replace(
                "{0}",
                existingEmployee.email
              )
            : translate("strEmployeeEGNExists").replace(
                "{0}",
                existingEmployee.egn
              ),
        requireMessage: false,
        confirmLabel: translate("Approve"),
        cancelLabel: translate("Cancel"),
        onConfirm: () => {
          // TODO - add new payroll to existing employee
        },
      });
      return;
    }

    const newEmployeeData: NewEmployeeDTO = {
      companyId: companyId,
      personalData: formData.personalData.data as PersonalDataDTO,
      identityCardData: formData.identityCardData.data as IdentityCardDataDTO,
      addressData: formData.addressData.data as NewAddressDTO,
    };
    setFormData(initialNewEmployeeFormData);

    const newEmployeePayroll = await addNewEmployeePayroll(newEmployeeData);
    dispatch(onEmployeePayrollUpdated(newEmployeePayroll));

    setIsLoading(false);
    toggleMenu();
    changeView("employees", "other");
  };

  return (
    <MainContainer data-testid="new-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            $isSelected={activeStep === step.id}
            disabled={isStepDisabled(step.id)}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        {steps.find((step) => step.id === activeStep)?.component}
      </ContentContainer>

      <NavigationContainer data-testid="navigation-container">
        {activeStep === steps.length ? (
          <NextButton
            label="Add"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid() || isLoading}
            data-testid="submit-button"
          />
        ) : (
          <NextButton
            label="strNext"
            onClick={handleNextClick}
            disabled={!isCurrentStepValid()}
            data-testid="next-button"
          />
        )}
      </NavigationContainer>
    </MainContainer>
  );
};

export default NewEmployeeSideMenu;
