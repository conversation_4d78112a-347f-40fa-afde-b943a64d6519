import { configureStore, ThunkAction, Action } from "@reduxjs/toolkit";
import { reducer as documentReducer } from "../features/documents/onboardingDocumentsActions";
import { reducer as payrollsReducer } from "../features/payroll/payrollsActions";
import { reducer as structureLevels } from "../features/company-structure/companyStructureActions";
import { reducer as employeesReducer } from "../features/employees/employeesActions";
import { reducer as companiesReducer } from "../features/companies/companiesActions";
import { reducer as senderaCompaniesReducer } from "../features/companies/senderaCompaniesAction";
import { reducer as eventsReducer } from "../features/absences/eventsActions";
import { reducer as employeePayrollsReducer } from "../features/payroll/employeePayrollActions";
import { reducer as notificationsReducer } from "../features/notifications/notificationsActions";
import { reducer as holidaysReducer } from "../features/holidays/holidayActions";
import {
  departmentsReducer,
  contractTypesReducer,
  categoriesReducer,
} from "../features/nomenclatures/NomenclaturesActions";

export const store = configureStore({
  reducer: {
    onboardingDocuments: documentReducer,
    payrolls: payrollsReducer,
    employeePayrolls: employeePayrollsReducer,
    companies: companiesReducer,
    structureLevels: structureLevels,
    employees: employeesReducer,
    senderaCompanies: senderaCompaniesReducer,
    trzEvents: eventsReducer,
    notifications: notificationsReducer,
    departments: departmentsReducer,
    contractTypes: contractTypesReducer,
    categories: categoriesReducer,
    holidays: holidaysReducer,
  },
});

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export type AppThunk<ReturnType, ActionType> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<ActionType>
>;

export interface ClearStateAction {
  type: "CLEAR_STATE";
}

export const clearStateAction = (): ClearStateAction => ({
  type: "CLEAR_STATE",
});

export const onClearState = () => (dispatch: AppDispatch) => {
  dispatch(clearStateAction());
};
